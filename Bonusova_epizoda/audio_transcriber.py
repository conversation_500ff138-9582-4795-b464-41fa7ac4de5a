#!/usr/bin/env python3
"""
Skript na prepísanie textu z audio súborov podcastu Krvavý Dobšinský
"""

import os
import speech_recognition as sr
from pydub import AudioSegment
from pathlib import Path
import time

def setup_recognizer():
    """Nastavenie rozpoznávača reči"""
    r = sr.Recognizer()
    # Nastavenie pre lepšie rozpoznávanie slovenčiny
    r.energy_threshold = 300
    r.dynamic_energy_threshold = True
    r.pause_threshold = 0.8
    r.phrase_threshold = 0.3
    return r

def convert_to_wav(audio_file_path):
    """Konvertuje audio súbor do WAV formátu pre lepšie rozpoznávanie"""
    try:
        # Načítanie audio súboru
        audio = AudioSegment.from_file(audio_file_path)
        
        # Konverzia na mono a 16kHz pre lepšie rozpoznávanie
        audio = audio.set_channels(1)
        audio = audio.set_frame_rate(16000)
        
        # Vytvorenie dočasného WAV súboru
        wav_path = audio_file_path.replace('.mp3', '_temp.wav')
        audio.export(wav_path, format="wav")
        
        return wav_path
    except Exception as e:
        print(f"Chyba pri konverzii {audio_file_path}: {e}")
        return None

def transcribe_audio_chunk(recognizer, audio_chunk, language='sk-SK'):
    """Prepíše časť audio súboru"""
    try:
        # Pokus o rozpoznanie s Google Speech Recognition
        text = recognizer.recognize_google(audio_chunk, language=language)
        return text
    except sr.UnknownValueError:
        return "[Nerozpoznané]"
    except sr.RequestError as e:
        return f"[Chyba služby: {e}]"
    except Exception as e:
        return f"[Chyba: {e}]"

def transcribe_audio_file(audio_file_path, output_dir):
    """Prepíše celý audio súbor"""
    print(f"\nSpracovávam: {os.path.basename(audio_file_path)}")
    
    # Konverzia do WAV formátu
    wav_path = convert_to_wav(audio_file_path)
    if not wav_path:
        return False
    
    try:
        recognizer = setup_recognizer()
        
        # Načítanie audio súboru
        with sr.AudioFile(wav_path) as source:
            # Úprava pre hluk v pozadí
            recognizer.adjust_for_ambient_noise(source, duration=1)
            
            # Načítanie celého audio súboru
            audio_data = recognizer.record(source)
        
        print("Rozpoznávam reč...")
        
        # Prepis textu
        transcript = transcribe_audio_chunk(recognizer, audio_data)
        
        # Vytvorenie názvu výstupného súboru
        base_name = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_prepis.txt")
        
        # Uloženie prepisu
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"PREPIS EPIZÓDY: {base_name}\n")
            f.write("=" * 50 + "\n\n")
            f.write(transcript)
            f.write(f"\n\n--- Koniec prepisu ---\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✓ Prepis uložený: {output_file}")
        
        # Vymazanie dočasného WAV súboru
        if os.path.exists(wav_path):
            os.remove(wav_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní {audio_file_path}: {e}")
        
        # Vymazanie dočasného WAV súboru
        if wav_path and os.path.exists(wav_path):
            os.remove(wav_path)
        
        return False

def transcribe_long_audio(audio_file_path, output_dir, chunk_length_ms=30000):
    """Prepíše dlhý audio súbor po častiach"""
    print(f"\nSpracovávam dlhý súbor po častiach: {os.path.basename(audio_file_path)}")
    
    try:
        # Načítanie audio súboru
        audio = AudioSegment.from_file(audio_file_path)
        
        # Konverzia na mono a 16kHz
        audio = audio.set_channels(1)
        audio = audio.set_frame_rate(16000)
        
        recognizer = setup_recognizer()
        full_transcript = []
        
        # Rozdelenie na časti
        chunks = [audio[i:i+chunk_length_ms] for i in range(0, len(audio), chunk_length_ms)]
        
        print(f"Rozdeľujem na {len(chunks)} častí...")
        
        for i, chunk in enumerate(chunks):
            print(f"Spracovávam časť {i+1}/{len(chunks)}...")
            
            # Uloženie časti ako dočasný WAV súbor
            temp_wav = f"temp_chunk_{i}.wav"
            chunk.export(temp_wav, format="wav")
            
            try:
                with sr.AudioFile(temp_wav) as source:
                    recognizer.adjust_for_ambient_noise(source, duration=0.5)
                    audio_data = recognizer.record(source)
                
                # Prepis časti
                text = transcribe_audio_chunk(recognizer, audio_data)
                full_transcript.append(f"[Časť {i+1}] {text}")
                
            except Exception as e:
                full_transcript.append(f"[Časť {i+1}] [Chyba: {e}]")
            
            # Vymazanie dočasného súboru
            if os.path.exists(temp_wav):
                os.remove(temp_wav)
            
            # Krátka pauza medzi časťami
            time.sleep(1)
        
        # Vytvorenie názvu výstupného súboru
        base_name = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_prepis_dlhy.txt")
        
        # Uloženie prepisu
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"PREPIS EPIZÓDY: {base_name}\n")
            f.write("=" * 50 + "\n\n")
            f.write("\n\n".join(full_transcript))
            f.write(f"\n\n--- Koniec prepisu ---\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✓ Prepis uložený: {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní dlhého súboru {audio_file_path}: {e}")
        return False

def main():
    """Hlavná funkcia"""
    # Cesty
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    # Vytvorenie adresára pre prepisy
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== Prepísanie audio súborov Krvavý Dobšinský ===")
    print(f"Zdrojový adresár: {audio_dir}")
    print(f"Výstupný adresár: {output_dir}")
    print()
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    for file in os.listdir(audio_dir):
        if file.endswith('.mp3'):
            audio_files.append(os.path.join(audio_dir, file))
    
    if not audio_files:
        print("Nenašli sa žiadne MP3 súbory!")
        return
    
    print(f"Nájdených {len(audio_files)} audio súborov")
    
    # Spýtanie sa používateľa, ktoré súbory chce prepísať
    print("\nMôžete:")
    print("1. Prepísať všetky súbory")
    print("2. Prepísať len jeden konkrétny súbor")
    print("3. Prepísať prvých 5 súborov (test)")
    
    choice = input("\nVyberte možnosť (1/2/3): ").strip()
    
    files_to_process = []
    
    if choice == "1":
        files_to_process = audio_files
    elif choice == "2":
        print("\nDostupné súbory:")
        for i, file in enumerate(audio_files[:10]):  # Zobrazí prvých 10
            print(f"{i+1}. {os.path.basename(file)}")
        
        try:
            file_index = int(input("Vyberte číslo súboru: ")) - 1
            if 0 <= file_index < len(audio_files):
                files_to_process = [audio_files[file_index]]
            else:
                print("Neplatné číslo!")
                return
        except ValueError:
            print("Neplatný vstup!")
            return
    elif choice == "3":
        files_to_process = audio_files[:5]
    else:
        print("Neplatná voľba!")
        return
    
    # Spracovanie súborov
    successful = 0
    failed = 0
    
    for audio_file in files_to_process:
        # Pre dlhšie súbory použiť rozdelenie na časti
        file_size = os.path.getsize(audio_file) / (1024 * 1024)  # MB
        
        if file_size > 10:  # Súbory väčšie ako 10MB
            success = transcribe_long_audio(audio_file, output_dir)
        else:
            success = transcribe_audio_file(audio_file, output_dir)
        
        if success:
            successful += 1
        else:
            failed += 1
        
        # Pauza medzi súbormi
        time.sleep(2)
    
    print(f"\n\nHotovo!")
    print(f"Úspešne prepísané: {successful}")
    print(f"Neúspešné: {failed}")
    print(f"Prepisy sú uložené v: {output_dir}")

if __name__ == "__main__":
    main()
