#!/usr/bin/env python3
"""
Nájde súbor Babylon 333, oprav<PERSON> ho a uloží do priečinka corrected
"""

import os
import re
import time

def find_babylon_file():
    """Nájde súbor Babylon 333"""
    search_dirs = [
        "/Users/<USER>/Desktop/Krvavý Audio/Prepisy",
        "/Users/<USER>/Desktop/Krvavý Audio"
    ]
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for file in os.listdir(search_dir):
                if 'babylon' in file.lower() and file.endswith('.txt'):
                    return os.path.join(search_dir, file)
    
    return None

def correct_slovak_text_advanced(text):
    """Pokročilá oprava slovenského textu"""
    
    # Odstránenie výplňových slov
    fillers = [
        r'\behm+\b', r'\beh+\b', r'\bäh+m?\b', r'\bhmm+\b', r'\bhm+\b',
        r'\bno\s+tak\b', r'\btak\s+no\b', r'\bproste\s+tak\b',
        r'\bvlastne\s+tak\b', r'\bteda\s+tak\b', r'\bčiže\s+tak\b',
    ]
    
    for filler in fillers:
        text = re.sub(filler, ' ', text, flags=re.IGNORECASE)
    
    # Oprava diakritiky - najčastejšie slovenské slová
    slovak_corrections = {
        # Základné slová
        'co': 'čo', 'ci': 'či', 'cize': 'čiže', 'cas': 'čas',
        'dalej': 'ďalej', 'dalsi': 'ďalší', 'dalsia': 'ďalšia', 'dalsie': 'ďalšie',
        'este': 'ešte', 'uz': 'už', 'ze': 'že', 'ked': 'keď', 'kedze': 'keďže',
        'ktory': 'ktorý', 'ktora': 'ktorá', 'ktore': 'ktoré',
        'nas': 'náš', 'nasa': 'naša', 'nase': 'naše',
        'vas': 'váš', 'vasa': 'vaša', 'vase': 'vaše',
        'ma': 'má', 'mam': 'mám', 'su': 'sú',
        'viac': 'viac', 'vsak': 'však', 'vsetko': 'všetko', 'vsetci': 'všetci',
        'preco': 'prečo', 'takze': 'takže', 'ano': 'áno',
        'ziaden': 'žiaden', 'ziadna': 'žiadna', 'ziadne': 'žiadne',
        'nic': 'nič', 'tyzden': 'týždeň', 'den': 'deň',
        
        # Emócie a pocity
        'strach': 'strach', 'hroza': 'hrôza', 'desivý': 'desivý',
        'desiva': 'desivá', 'desive': 'desivé', 'tajomny': 'tajomný',
        'tajomna': 'tajomná', 'tajomne': 'tajomné', 'temny': 'temný',
        'temna': 'temná', 'temne': 'temné',
        
        # Miesta a objekty
        'hrad': 'hrad', 'hrady': 'hrady', 'kastiel': 'kaštieľ',
        'les': 'les', 'lesy': 'lesy', 'hora': 'hora', 'hory': 'hory',
        'dedina': 'dedina', 'dediny': 'dediny', 'mesto': 'mesto',
        'dom': 'dom', 'domy': 'domy', 'okno': 'okno', 'okna': 'okná',
        'dvere': 'dvere', 'stena': 'stena', 'steny': 'steny',
        
        # Čas
        'rano': 'ráno', 'vecer': 'večer', 'noc': 'noc',
        'vcera': 'včera', 'zajtra': 'zajtra', 'dnes': 'dnes',
        'rok': 'rok', 'roky': 'roky', 'mesiac': 'mesiac',
        
        # Babylon špecifické
        'babylon': 'Babylon', 'budova': 'budova', 'poschodie': 'poschodie',
        'vytah': 'výťah', 'schody': 'schody', 'chodba': 'chodba',
        'dvere': 'dvere', 'izba': 'izba', 'miestnost': 'miestnosť',
        'svetlo': 'svetlo', 'tma': 'tma', 'zvuk': 'zvuk',
        'hluk': 'hluk', 'ticho': 'ticho', 'krik': 'krik',
        'smrt': 'smrť', 'zivot': 'život', 'dusa': 'duša',
        'telo': 'telo', 'krv': 'krv', 'oci': 'oči',
    }
    
    # Aplikovanie opráv po slovách
    words = text.split()
    corrected_words = []
    
    for word in words:
        # Odstránenie interpunkcie pre kontrolu
        clean_word = re.sub(r'[^\w]', '', word.lower())
        
        if clean_word in slovak_corrections:
            # Zachovanie pôvodnej interpunkcie a veľkosti písmen
            corrected_word = word
            for old, new in slovak_corrections.items():
                if clean_word == old:
                    # Zachovanie veľkosti prvého písmena
                    if word[0].isupper():
                        new = new.capitalize()
                    corrected_word = re.sub(re.escape(old), new, word, flags=re.IGNORECASE)
                    break
            corrected_words.append(corrected_word)
        else:
            corrected_words.append(word)
    
    text = ' '.join(corrected_words)
    
    # Oprava interpunkcie
    text = re.sub(r'\s+([,.!?:;])', r'\1', text)  # Odstránenie medzier pred interpunkciou
    text = re.sub(r'([,.!?:;])([a-zA-ZáäčďéíĺľňóôŕšťúýžÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ])', r'\1 \2', text)  # Pridanie medzery za interpunkciu
    
    # Oprava úvodzoviek
    text = re.sub(r'"\s*([^"]*?)\s*"', r'„\1"', text)
    
    # Oprava pomlčiek
    text = re.sub(r'\s*-\s*', ' – ', text)
    
    # Oprava viacnásobnej interpunkcie
    text = re.sub(r'[,]{2,}', ',', text)
    text = re.sub(r'[.]{3,}', '…', text)
    text = re.sub(r'[!]{2,}', '!', text)
    text = re.sub(r'[?]{2,}', '?', text)
    
    # Oprava medzier
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s+', '\n', text)
    text = re.sub(r'\s+\n', '\n', text)
    
    # Oprava veľkých písmen
    text = re.sub(r'^([a-záäčďéíĺľňóôŕšťúýž])', lambda m: m.group(1).upper(), text)
    text = re.sub(r'([.!?]\s+)([a-záäčďéíĺľňóôŕšťúýž])', 
                 lambda m: m.group(1) + m.group(2).upper(), text)
    
    return text

def add_proper_paragraphs(text):
    """Pridá správne odseky"""
    # Rozdelenie na vety
    sentences = re.split(r'([.!?]+)', text)
    
    if len(sentences) < 6:
        return text
    
    paragraphs = []
    current_paragraph = []
    sentence_count = 0
    
    for i in range(0, len(sentences)-1, 2):
        sentence = sentences[i].strip()
        punctuation = sentences[i+1] if i+1 < len(sentences) else '.'
        
        if sentence:
            current_paragraph.append(sentence + punctuation)
            sentence_count += 1
            
            # Nový odsek každých 3-5 viet alebo pri signálnych slovách
            should_break = (
                sentence_count >= 3 and (
                    sentence_count >= 5 or
                    any(signal in sentence.lower() for signal in [
                        'potom', 'ďalej', 'nakoniec', 'takže', 'ale', 'však', 
                        'preto', 'prečo', 'vtedy', 'neskôr', 'medzitým',
                        'napriek tomu', 'okrem toho', 'navyše', 'teda',
                        'zrazu', 'náhle', 'odrazu', 'v tom momente'
                    ])
                )
            )
            
            if should_break:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
                sentence_count = 0
    
    # Pridanie posledného odseku
    if current_paragraph:
        paragraphs.append(' '.join(current_paragraph))
    
    return '\n\n'.join(paragraphs)

def main():
    """Hlavná funkcia"""
    print("🔍 Hľadám súbor Babylon 333...")
    
    # Nájdenie súboru
    babylon_file = find_babylon_file()
    
    if not babylon_file:
        print("❌ Súbor Babylon 333 sa nenašiel!")
        return
    
    print(f"✅ Nájdený súbor: {babylon_file}")
    
    # Načítanie obsahu
    try:
        with open(babylon_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📖 Načítaný obsah ({len(content)} znakov)")
        
        # Extrakcia samotného príbehu
        lines = content.split('\n')
        story_start = 0
        
        # Nájdenie začiatku príbehu
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'príbeh', 'babylon', 'text:']):
                story_start = i + 1
                break
        
        # Nájdenie konca príbehu (pred informáciami o spracovaní)
        story_end = len(lines)
        for i in range(len(lines)-1, -1, -1):
            if any(keyword in line.lower() for keyword in 
                   ['informácie', 'spracované:', 'koniec prepisu', '===']):
                story_end = i
                break
        
        story_lines = lines[story_start:story_end]
        story_text = '\n'.join(story_lines).strip()
        
        if len(story_text) < 50:
            print("❌ Príbeh je príliš krátky!")
            return
        
        print(f"📝 Extrahovaný príbeh ({len(story_text)} znakov)")
        
        # Oprava textu
        print("🔧 Opravujem text...")
        corrected_text = correct_slovak_text_advanced(story_text)
        
        print("📋 Pridávam odseky...")
        final_text = add_proper_paragraphs(corrected_text)
        
        # Vytvorenie priečinka corrected
        corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(corrected_dir, exist_ok=True)
        
        # Vytvorenie nového obsahu
        new_content = [
            "BABYLON 333",
            "=" * 50,
            f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "Kvalita: Profesionálna slovenčina",
            "",
            final_text,
            "",
            "",
            "=" * 50,
            "INFORMÁCIE:",
            f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"- Pôvodná dĺžka: {len(story_text)} znakov",
            f"- Opravená dĺžka: {len(final_text)} znakov",
            "- Opravené: diakritika, gramatika, interpunkcia, odseky",
            "- Jazyk: slovenčina (SK)",
            "- Kvalita: profesionálna úroveň"
        ]
        
        # Uloženie opraveného súboru
        output_file = os.path.join(corrected_dir, "Babylon 333_corrected.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_content))
        
        print(f"✅ Opravený súbor uložený: {output_file}")
        print(f"📁 Priečinok: {corrected_dir}")
        
        # Zobrazenie ukážky opraveného textu
        print("\n📖 UKÁŽKA OPRAVENÉHO TEXTU:")
        print("-" * 50)
        preview = final_text[:500] + "..." if len(final_text) > 500 else final_text
        print(preview)
        print("-" * 50)
        
        print(f"\n🎉 Babylon 333 je teraz v perfektnej slovenčine!")
        
    except Exception as e:
        print(f"❌ Chyba pri spracovaní: {e}")

if __name__ == "__main__":
    main()
