#!/usr/bin/env python3
"""
Presunie všetky TXT súbory z priečinka Krvavý Audio do podpriečinka Prepisy
"""

import os
import shutil
from pathlib import Path

def move_txt_files():
    """Presunie všetky TXT súbory do priečinka Prepisy"""
    
    # Cesty
    source_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    target_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("=== PRESUN TXT SÚBOROV ===")
    print(f"Zdrojový priečinok: {source_dir}")
    print(f"Cieľový priečinok: {target_dir}")
    print()
    
    # Vytvorenie cieľového priečinka ak neexistuje
    os.makedirs(target_dir, exist_ok=True)
    print(f"✓ Cieľový priečinok pripravený: {target_dir}")
    
    # Nájdenie všetkých TXT súborov v zdrojovom priečinku
    txt_files = []
    
    try:
        for item in os.listdir(source_dir):
            item_path = os.path.join(source_dir, item)
            
            # Kontrola či je to súbor a má príponu .txt
            if os.path.isfile(item_path) and item.lower().endswith('.txt'):
                txt_files.append(item_path)
        
        print(f"Nájdených {len(txt_files)} TXT súborov")
        
        if not txt_files:
            print("✓ Žiadne TXT súbory na presun")
            return
        
        # Zobrazenie nájdených súborov
        print("\nNájdené TXT súbory:")
        for i, file_path in enumerate(txt_files, 1):
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"{i:2d}. {file_name} ({file_size} bytov)")
        
        print()
        
        # Presun každého súboru
        moved_count = 0
        failed_count = 0
        
        for file_path in txt_files:
            file_name = os.path.basename(file_path)
            target_path = os.path.join(target_dir, file_name)
            
            try:
                # Kontrola či súbor už existuje v cieľovom priečinku
                if os.path.exists(target_path):
                    print(f"⚠️  Súbor už existuje: {file_name}")
                    
                    # Vytvorenie záložnej kópie
                    backup_name = f"{os.path.splitext(file_name)[0]}_backup{os.path.splitext(file_name)[1]}"
                    backup_path = os.path.join(target_dir, backup_name)
                    
                    if os.path.exists(backup_path):
                        # Ak existuje aj záložka, pridáme časovú značku
                        import time
                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                        backup_name = f"{os.path.splitext(file_name)[0]}_{timestamp}{os.path.splitext(file_name)[1]}"
                        backup_path = os.path.join(target_dir, backup_name)
                    
                    shutil.move(target_path, backup_path)
                    print(f"   Existujúci súbor premenovaný na: {backup_name}")
                
                # Presun súboru
                shutil.move(file_path, target_path)
                print(f"✓ Presunuté: {file_name}")
                moved_count += 1
                
            except Exception as e:
                print(f"✗ Chyba pri presune {file_name}: {e}")
                failed_count += 1
        
        print(f"\n=== VÝSLEDOK ===")
        print(f"Úspešne presunuté: {moved_count}")
        print(f"Neúspešné: {failed_count}")
        print(f"Celkom súborov: {len(txt_files)}")
        
        if moved_count > 0:
            print(f"\n📁 TXT súbory sú teraz v: {target_dir}")
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní priečinka: {e}")

def list_files_in_directories():
    """Zobrazí obsah oboch priečinkov"""
    
    source_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    target_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("\n=== OBSAH PRIEČINKOV ===")
    
    # Hlavný priečinok
    print(f"\nHlavný priečinok ({source_dir}):")
    try:
        items = os.listdir(source_dir)
        txt_files = [f for f in items if f.lower().endswith('.txt')]
        mp3_files = [f for f in items if f.lower().endswith('.mp3')]
        other_files = [f for f in items if not f.lower().endswith(('.txt', '.mp3')) and os.path.isfile(os.path.join(source_dir, f))]
        directories = [f for f in items if os.path.isdir(os.path.join(source_dir, f))]
        
        print(f"  TXT súbory: {len(txt_files)}")
        print(f"  MP3 súbory: {len(mp3_files)}")
        print(f"  Ostatné súbory: {len(other_files)}")
        print(f"  Priečinky: {len(directories)}")
        
        if txt_files:
            print("  TXT súbory:")
            for txt_file in txt_files[:5]:  # Zobrazí prvých 5
                print(f"    - {txt_file}")
            if len(txt_files) > 5:
                print(f"    ... a ďalších {len(txt_files)-5}")
        
    except Exception as e:
        print(f"  ✗ Chyba pri čítaní: {e}")
    
    # Priečinok Prepisy
    print(f"\nPriečinok Prepisy ({target_dir}):")
    try:
        if os.path.exists(target_dir):
            items = os.listdir(target_dir)
            txt_files = [f for f in items if f.lower().endswith('.txt')]
            other_files = [f for f in items if not f.lower().endswith('.txt')]
            
            print(f"  TXT súbory: {len(txt_files)}")
            print(f"  Ostatné súbory: {len(other_files)}")
            
            if txt_files:
                print("  TXT súbory:")
                for txt_file in txt_files[:10]:  # Zobrazí prvých 10
                    print(f"    - {txt_file}")
                if len(txt_files) > 10:
                    print(f"    ... a ďalších {len(txt_files)-10}")
        else:
            print("  Priečinok neexistuje")
            
    except Exception as e:
        print(f"  ✗ Chyba pri čítaní: {e}")

def main():
    """Hlavná funkcia"""
    print("=== SPRÁVCA TXT SÚBOROV ===")
    print("1. Presunúť všetky TXT súbory do priečinka Prepisy")
    print("2. Zobraziť obsah priečinkov")
    print("3. Ukončiť")
    
    while True:
        try:
            choice = input("\nVyberte možnosť (1/2/3): ").strip()
            
            if choice == "1":
                move_txt_files()
                break
            elif choice == "2":
                list_files_in_directories()
            elif choice == "3":
                print("Ukončené.")
                break
            else:
                print("Neplatná voľba! Zadajte 1, 2 alebo 3.")
                
        except KeyboardInterrupt:
            print("\n\nUkončené používateľom.")
            break

if __name__ == "__main__":
    main()
