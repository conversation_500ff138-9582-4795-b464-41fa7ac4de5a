#!/usr/bin/env python3
"""
Finálny procesor - pripraví texty pre web publikovanie
Vymaže metadata, skontroluje kvalitu a vytvorí čisté texty
"""

import os
import re
import time
from pathlib import Path

class WebReadyProcessor:
    def __init__(self):
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        self.web_ready_dir = "/Users/<USER>/Desktop/Krvavý Audio/web_ready"
        os.makedirs(self.web_ready_dir, exist_ok=True)
    
    def get_corrected_files(self):
        """Získa všetky opravené súbory"""
        if not os.path.exists(self.corrected_dir):
            return []
        
        corrected_files = []
        for file in sorted(os.listdir(self.corrected_dir)):
            if file.endswith('_OpenAI_corrected.txt'):
                corrected_files.append({
                    'file_name': file,
                    'file_path': os.path.join(self.corrected_dir, file),
                    'episode_name': file.replace('_OpenAI_corrected.txt', '')
                })
        
        return corrected_files
    
    def extract_clean_text(self, file_path):
        """Extrahuje čistý text bez metadát"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Nájdenie začiatku čistého textu (po hlavičke)
            text_start = 0
            for i, line in enumerate(lines):
                # Preskočenie hlavičky až po prvý prázdny riadok po "Podcast: Krvavý Dobšinský"
                if line.strip() == "" and i > 5:
                    text_start = i + 1
                    break
            
            # Nájdenie konca čistého textu (pred informáciami)
            text_end = len(lines)
            for i in range(len(lines)-1, -1, -1):
                if line.strip().startswith('=') and 'INFORMÁCIE' in content[content.find(lines[i]):]:
                    text_end = i
                    break
                elif lines[i].strip() == "" and i < len(lines) - 10:
                    # Hľadáme posledný prázdny riadok pred informáciami
                    for j in range(i+1, len(lines)):
                        if lines[j].strip().startswith('='):
                            text_end = i
                            break
                    if text_end != len(lines):
                        break
            
            # Extrakcia čistého textu
            clean_lines = lines[text_start:text_end]
            clean_text = '\n'.join(clean_lines).strip()
            
            return clean_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def check_text_quality(self, text, episode_name):
        """Skontroluje kvalitu textu"""
        issues = []
        
        # Kontrola dĺžky
        if len(text) < 100:
            issues.append(f"Text príliš krátky ({len(text)} znakov)")
        
        # Kontrola slovenských znakov
        slovak_chars = set('áäčďéíĺľňóôŕšťúýž')
        text_lower = text.lower()
        has_slovak = any(char in text_lower for char in slovak_chars)
        if not has_slovak and len(text) > 500:
            issues.append("Chýba slovenská diakritika")
        
        # Kontrola častých chýb
        common_errors = [
            ('co ', 'čo '), ('ci ', 'či '), ('ze ', 'že '), ('ked ', 'keď '),
            ('uz ', 'už '), ('este ', 'ešte '), ('dalej', 'ďalej'),
            ('ktory', 'ktorý'), ('preco', 'prečo'), ('takze', 'takže')
        ]
        
        for error, correct in common_errors:
            if error in text_lower:
                issues.append(f"Možná chyba: '{error}' -> '{correct}'")
        
        # Kontrola interpunkcie
        if '""' in text:
            issues.append("Nesprávne úvodzovky - použiť slovenské")
        
        # Kontrola výplňových slov
        fillers = ['ehm', 'hmm', 'no tak', 'vlastne tak']
        for filler in fillers:
            if filler in text_lower:
                issues.append(f"Výplňové slovo: '{filler}'")
        
        return issues
    
    def format_text_for_web(self, text, episode_name):
        """Naformátuje text pre web publikovanie"""
        
        # Základné čistenie
        text = text.strip()
        
        # Oprava medzier
        text = re.sub(r'\s+', ' ', text)  # Viacnásobné medzery
        text = re.sub(r'\n\s+', '\n', text)  # Medzery na začiatku riadkov
        text = re.sub(r'\s+\n', '\n', text)  # Medzery na konci riadkov
        
        # Oprava odsekov - rozdelenie na logické celky
        sentences = re.split(r'([.!?]+)', text)
        formatted_paragraphs = []
        current_paragraph = []
        sentence_count = 0
        
        for i in range(0, len(sentences)-1, 2):
            sentence = sentences[i].strip()
            punctuation = sentences[i+1] if i+1 < len(sentences) else '.'
            
            if sentence:
                current_paragraph.append(sentence + punctuation)
                sentence_count += 1
                
                # Nový odsek po 3-5 vetách alebo pri signálnych slovách
                should_break = (
                    sentence_count >= 3 and (
                        sentence_count >= 5 or
                        any(signal in sentence.lower() for signal in [
                            'potom', 'ďalej', 'nakoniec', 'takže', 'ale', 'však', 
                            'preto', 'vtedy', 'neskôr', 'medzitým', 'zrazu', 'náhle',
                            'o chvíľu', 'za chvíľu', 'po chvíli', 'keď som', 'keď sa'
                        ])
                    )
                )
                
                if should_break:
                    formatted_paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
                    sentence_count = 0
        
        # Pridanie posledného odseku
        if current_paragraph:
            formatted_paragraphs.append(' '.join(current_paragraph))
        
        # Spojenie odsekov
        formatted_text = '\n\n'.join(formatted_paragraphs)
        
        # Finálne úpravy
        formatted_text = re.sub(r'\n{3,}', '\n\n', formatted_text)  # Max 2 nové riadky
        formatted_text = formatted_text.strip()
        
        return formatted_text
    
    def create_web_ready_file(self, clean_text, episode_info):
        """Vytvorí súbor pripravený na web"""
        try:
            episode_name = episode_info['episode_name']
            
            # Vytvorenie web-ready súboru
            web_file = os.path.join(self.web_ready_dir, f"{episode_name}.txt")
            
            # Hlavička pre web
            web_content = [
                f"# {episode_name}",
                "",
                f"*Epizóda podcastu Krvavý Dobšinský*",
                "",
                "---",
                "",
                clean_text,
                "",
                "---",
                "",
                f"*Spracované: {time.strftime('%Y-%m-%d')}*",
                f"*Kvalita: Profesionálna slovenčina*"
            ]
            
            with open(web_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(web_content))
            
            return web_file
            
        except Exception as e:
            print(f"❌ Chyba pri vytváraní web súboru: {e}")
            return None
    
    def process_all_files(self):
        """Spracuje všetky súbory"""
        print("🌐 === PRÍPRAVA TEXTOV PRE WEB PUBLIKOVANIE ===")
        print(f"📁 Zdrojový priečinok: {self.corrected_dir}")
        print(f"📁 Cieľový priečinok: {self.web_ready_dir}")
        print()
        
        # Získanie súborov
        corrected_files = self.get_corrected_files()
        
        if not corrected_files:
            print("❌ Nenašli sa žiadne opravené súbory!")
            return
        
        print(f"📊 Spracovávam {len(corrected_files)} súborov")
        print()
        
        successful = 0
        issues_found = 0
        total_issues = []
        
        for i, file_info in enumerate(corrected_files, 1):
            episode_name = file_info['episode_name']
            file_path = file_info['file_path']
            
            print(f"[{i:2d}/{len(corrected_files)}] {episode_name}")
            
            # Extrakcia čistého textu
            clean_text = self.extract_clean_text(file_path)
            if not clean_text:
                print(f"   ❌ Nepodarilo sa extrahovať text")
                continue
            
            print(f"   📖 Extrahovaný text: {len(clean_text)} znakov")
            
            # Kontrola kvality
            quality_issues = self.check_text_quality(clean_text, episode_name)
            if quality_issues:
                print(f"   ⚠️  Nájdených {len(quality_issues)} problémov:")
                for issue in quality_issues[:3]:  # Zobrazenie prvých 3
                    print(f"      - {issue}")
                if len(quality_issues) > 3:
                    print(f"      ... a ďalších {len(quality_issues)-3}")
                issues_found += 1
                total_issues.extend(quality_issues)
            else:
                print(f"   ✅ Kvalita: OK")
            
            # Formátovanie pre web
            formatted_text = self.format_text_for_web(clean_text, episode_name)
            print(f"   📝 Naformátovaný text: {len(formatted_text)} znakov")
            
            # Vytvorenie web súboru
            web_file = self.create_web_ready_file(formatted_text, file_info)
            if web_file:
                print(f"   💾 Web súbor: {os.path.basename(web_file)}")
                successful += 1
            else:
                print(f"   ❌ Nepodarilo sa vytvoriť web súbor")
            
            print()
        
        # Finálny súhrn
        print(f"🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne spracované: {successful}")
        print(f"⚠️  Súbory s problémami: {issues_found}")
        print(f"📊 Celkom problémov: {len(total_issues)}")
        print(f"📁 Web-ready súbory: {self.web_ready_dir}")
        
        # Najčastejšie problémy
        if total_issues:
            print(f"\n📋 Najčastejšie problémy:")
            issue_counts = {}
            for issue in total_issues:
                key = issue.split(':')[0] if ':' in issue else issue
                issue_counts[key] = issue_counts.get(key, 0) + 1
            
            for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   {count}x {issue}")
        
        # Zobrazenie obsahu web_ready priečinka
        try:
            web_files = [f for f in os.listdir(self.web_ready_dir) if f.endswith('.txt')]
            print(f"\n📋 Web-ready súbory ({len(web_files)}):")
            for i, file in enumerate(sorted(web_files)[:10], 1):
                print(f"   {i:2d}. {file}")
            
            if len(web_files) > 10:
                print(f"   ... a ďalších {len(web_files)-10} súborov")
                
        except Exception as e:
            print(f"❌ Chyba pri zobrazení web súborov: {e}")
        
        print(f"\n🎉 Texty sú pripravené na web publikovanie!")
        print(f"📚 {successful} epizód v čistom formáte")
        print(f"🌐 Pripravené pre CMS, blog alebo webstránku")

def main():
    """Hlavná funkcia"""
    processor = WebReadyProcessor()
    processor.process_all_files()

if __name__ == "__main__":
    main()
