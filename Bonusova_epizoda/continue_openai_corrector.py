#!/usr/bin/env python3
"""
Pokračovanie v OpenAI oprave - ďalších 10 epizód
"""

import os
import time
import requests
import json

class ContinueOpenAICorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_next_episodes_to_process(self, max_count=10):
        """Získa ďalšie epizódy na spracovanie"""
        if not os.path.exists(self.prepisy_dir):
            return []
        
        # Nájdenie všetkých _cleaned.txt súborov
        cleaned_files = []
        for file in os.listdir(self.prepisy_dir):
            if file.endswith('_cleaned.txt'):
                cleaned_files.append(file)
        
        cleaned_files.sort()
        
        # Nájdenie už opravených súborov
        corrected_files = []
        if os.path.exists(self.corrected_dir):
            for file in os.listdir(self.corrected_dir):
                if file.endswith('_OpenAI_corrected.txt'):
                    base_name = file.replace('_OpenAI_corrected.txt', '')
                    corrected_files.append(f"{base_name}_cleaned.txt")
        
        # Nájdenie neopravených súborov
        episodes_to_process = []
        for file in cleaned_files:
            if file not in corrected_files:
                base_name = file.replace('_cleaned.txt', '')
                episodes_to_process.append({
                    'base_name': base_name,
                    'file_name': file,
                    'file_path': os.path.join(self.prepisy_dir, file)
                })
                
                if len(episodes_to_process) >= max_count:
                    break
        
        return episodes_to_process
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai(self, text, episode_name):
        """Opraví text pomocou OpenAI API"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 6000:
                text = text[:6000] + "\n\n[Text skrátený]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský":

1. Oprav gramatiku a preklepy
2. Oprav diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - slovenské úvodzovky „" a pomlčky –
4. Rozdeľ na logické odseky
5. Odstráň výplňové slová (ehm, no, tak, teda, atď.)
6. Zachovaj obsah, štýl a atmosféru
7. Vráť len opravený text

EPIZÓDA: {episode_name}

TEXT:
{text}"""
            
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk. Opravuješ texty z horor podcastu s maximálnou presnosťou."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 3000,
                "temperature": 0.2
            }
            
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                corrected_text = result['choices'][0]['message']['content'].strip()
                return corrected_text
            else:
                print(f"❌ API chyba: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, episode_info):
        """Uloží opravený text"""
        try:
            base_name = episode_info['base_name']
            output_file = os.path.join(self.corrected_dir, f"{base_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {base_name}",
                "=" * 60,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "Podcast: Krvavý Dobšinský",
                "",
                corrected_text,
                "",
                "",
                "=" * 60,
                "INFORMÁCIE:",
                f"- Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini",
                f"- Pôvodný súbor: {episode_info['file_name']}",
                f"- Dĺžka: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika, interpunkcia",
                "- Jazyk: slovenčina (SK) - profesionálna úroveň"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_episodes(self):
        """Spracuje ďalšie epizódy"""
        print("🔄 === POKRAČOVANIE OPENAI OPRAVY ===")
        print()
        
        # Kontrola aktuálneho stavu
        try:
            corrected_files = [f for f in os.listdir(self.corrected_dir) 
                             if f.endswith('_OpenAI_corrected.txt')]
            print(f"📊 Už opravených: {len(corrected_files)} epizód")
        except:
            print(f"📊 Už opravených: 0 epizód")
        
        episodes = self.get_next_episodes_to_process(max_count=10)
        
        if not episodes:
            print("🎉 Všetky epizódy už sú opravené!")
            return
        
        print(f"🎯 Spracovávam ďalších {len(episodes)} epizód:")
        for i, ep in enumerate(episodes, 1):
            print(f"   {i}. {ep['base_name']}")
        print()
        
        successful = 0
        failed = 0
        
        for i, episode in enumerate(episodes, 1):
            print(f"[{i}/{len(episodes)}] {episode['base_name']}")
            
            # Extrakcia textu
            story_text = self.extract_story_text(episode['file_path'])
            if not story_text or len(story_text) < 50:
                print(f"   ❌ Neplatný text")
                failed += 1
                continue
            
            print(f"   📖 Text: {len(story_text)} znakov")
            
            # Oprava
            print(f"   🤖 OpenAI...")
            corrected_text = self.correct_text_with_openai(story_text, episode['base_name'])
            if not corrected_text:
                print(f"   ❌ Oprava zlyhala")
                failed += 1
                continue
            
            print(f"   ✅ Opravené: {len(corrected_text)} znakov")
            
            # Uloženie
            output_file = self.save_corrected_text(corrected_text, episode)
            if output_file:
                print(f"   💾 Uložené: {os.path.basename(output_file)}")
                successful += 1
            else:
                print(f"   ❌ Uloženie zlyhalo")
                failed += 1
            
            # Pauza
            if i < len(episodes):
                print("   ⏳ Pauza 2s...")
                time.sleep(2)
            
            print()
        
        # Finálny stav
        try:
            total_corrected = len([f for f in os.listdir(self.corrected_dir) 
                                 if f.endswith('_OpenAI_corrected.txt')])
        except:
            total_corrected = successful
        
        print(f"🏁 === VÝSLEDOK DÁVKY ===")
        print(f"✅ Úspešne v tejto dávke: {successful}")
        print(f"❌ Neúspešne v tejto dávke: {failed}")
        print(f"📊 Celkom opravených: {total_corrected} epizód")
        print(f"📁 Súbory: {self.corrected_dir}")

def main():
    """Hlavná funkcia"""
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = ContinueOpenAICorrector(api_key)
    corrector.process_episodes()

if __name__ == "__main__":
    main()
