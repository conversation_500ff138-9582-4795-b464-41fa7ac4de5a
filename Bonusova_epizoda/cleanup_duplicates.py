#!/usr/bin/env python3
"""
Vyčistí duplikované a nadbytočné súbory - presunie ich do koša
<PERSON>ch<PERSON> len najkvalitnejšie verzie súborov
"""

import os
import shutil
import time
from pathlib import Path

def move_to_trash(file_path):
    """Presunie súbor do koša (macOS)"""
    try:
        # Na macOS použijeme osascript na presun do koša
        import subprocess
        result = subprocess.run([
            'osascript', '-e', 
            f'tell application "Finder" to delete POSIX file "{file_path}"'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            return True, "Presunuté do koša"
        else:
            # Fallback - presun do Trash priečinka
            trash_dir = os.path.expanduser("~/.Trash")
            if os.path.exists(trash_dir):
                file_name = os.path.basename(file_path)
                trash_path = os.path.join(trash_dir, file_name)
                
                # Ak súbor v ko<PERSON><PERSON> už existuje, pridáme timestamp
                if os.path.exists(trash_path):
                    name, ext = os.path.splitext(file_name)
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    trash_path = os.path.join(trash_dir, f"{name}_{timestamp}{ext}")
                
                shutil.move(file_path, trash_path)
                return True, f"Presunuté do ~/.Trash ako {os.path.basename(trash_path)}"
            else:
                return False, "Nepodarilo sa presunúť do koša"
                
    except Exception as e:
        return False, f"Chyba: {e}"

def analyze_files(directory):
    """Analyzuje súbory a identifikuje duplikáty"""
    
    files_by_base_name = {}
    
    # Zoskupenie súborov podľa základného názvu
    for file in os.listdir(directory):
        if not file.lower().endswith('.txt'):
            continue
            
        file_path = os.path.join(directory, file)
        if not os.path.isfile(file_path):
            continue
        
        # Určenie základného názvu a typu
        base_name = file
        file_type = "original"
        
        if file.endswith('_cleaned.txt'):
            base_name = file.replace('_cleaned.txt', '.txt')
            file_type = "cleaned"
        elif file.endswith('_SK_corrected.txt'):
            base_name = file.replace('_SK_corrected.txt', '.txt')
            file_type = "sk_corrected"
        elif file.endswith('_info.txt'):
            base_name = file.replace('_info.txt', '.txt')
            file_type = "info"
        elif file.endswith('_prepis.txt'):
            base_name = file.replace('_prepis.txt', '.txt')
            file_type = "prepis"
        elif file.endswith('_kompletny_prepis.txt'):
            base_name = file.replace('_kompletny_prepis.txt', '.txt')
            file_type = "kompletny_prepis"
        
        if base_name not in files_by_base_name:
            files_by_base_name[base_name] = {}
        
        files_by_base_name[base_name][file_type] = {
            'file_name': file,
            'file_path': file_path,
            'size': os.path.getsize(file_path),
            'modified': os.path.getmtime(file_path)
        }
    
    return files_by_base_name

def determine_files_to_keep_and_remove(files_by_base_name):
    """Určí ktoré súbory ponechať a ktoré odstrániť"""
    
    files_to_keep = []
    files_to_remove = []
    
    for base_name, versions in files_by_base_name.items():
        print(f"\n📄 Analyzujem: {base_name}")
        
        # Priorita typov súborov (od najlepšieho po najhorší)
        priority_order = [
            'sk_corrected',    # Najlepšie - slovensky opravené
            'cleaned',         # Vyčistené
            'kompletny_prepis', # Kompletný prepis
            'prepis',          # Základný prepis
            'original',        # Pôvodný
            'info'             # Len informácie
        ]
        
        # Nájdenie najlepšej verzie
        best_version = None
        best_type = None
        
        for file_type in priority_order:
            if file_type in versions:
                best_version = versions[file_type]
                best_type = file_type
                break
        
        if best_version:
            files_to_keep.append({
                'file_path': best_version['file_path'],
                'file_name': best_version['file_name'],
                'type': best_type,
                'base_name': base_name
            })
            print(f"   ✅ Ponechám: {best_version['file_name']} (typ: {best_type})")
            
            # Všetky ostatné verzie označiť na odstránenie
            for file_type, file_info in versions.items():
                if file_type != best_type:
                    files_to_remove.append({
                        'file_path': file_info['file_path'],
                        'file_name': file_info['file_name'],
                        'type': file_type,
                        'base_name': base_name,
                        'reason': f'Duplikát - lepšia verzia: {best_type}'
                    })
                    print(f"   🗑️  Odstránim: {file_info['file_name']} (typ: {file_type})")
    
    return files_to_keep, files_to_remove

def cleanup_directory(directory):
    """Vyčistí priečinok od duplikátov"""
    
    print("🧹 === ČISTENIE DUPLIKOVANÝCH SÚBOROV ===")
    print(f"📁 Priečinok: {directory}")
    print()
    
    if not os.path.exists(directory):
        print(f"❌ Priečinok {directory} neexistuje!")
        return
    
    # Analýza súborov
    print("🔍 Analyzujem súbory...")
    files_by_base_name = analyze_files(directory)
    
    if not files_by_base_name:
        print("❌ Nenašli sa žiadne TXT súbory!")
        return
    
    print(f"📊 Nájdených {len(files_by_base_name)} základných názvov súborov")
    
    # Určenie čo ponechať a čo odstrániť
    files_to_keep, files_to_remove = determine_files_to_keep_and_remove(files_by_base_name)
    
    print(f"\n📋 SÚHRN:")
    print(f"   ✅ Ponechám: {len(files_to_keep)} súborov")
    print(f"   🗑️  Odstránim: {len(files_to_remove)} súborov")
    
    if not files_to_remove:
        print("\n🎉 Žiadne duplikáty na odstránenie!")
        return
    
    # Potvrdenie od používateľa
    print(f"\n⚠️  POZOR: Chystám sa presunúť {len(files_to_remove)} súborov do koša!")
    print("📋 Súbory na odstránenie:")
    
    for i, file_info in enumerate(files_to_remove[:10], 1):
        print(f"   {i:2d}. {file_info['file_name']} ({file_info['reason']})")
    
    if len(files_to_remove) > 10:
        print(f"   ... a ďalších {len(files_to_remove)-10} súborov")
    
    print(f"\n✅ Súbory ktoré zostanú:")
    for i, file_info in enumerate(files_to_keep[:10], 1):
        print(f"   {i:2d}. {file_info['file_name']} (typ: {file_info['type']})")
    
    if len(files_to_keep) > 10:
        print(f"   ... a ďalších {len(files_to_keep)-10} súborov")
    
    # Automatické pokračovanie (pre skript)
    proceed = True
    
    if proceed:
        print(f"\n🗑️  Presúvam súbory do koša...")
        
        successful = 0
        failed = 0
        
        for file_info in files_to_remove:
            file_path = file_info['file_path']
            file_name = file_info['file_name']
            
            success, message = move_to_trash(file_path)
            
            if success:
                print(f"   ✅ {file_name} - {message}")
                successful += 1
            else:
                print(f"   ❌ {file_name} - {message}")
                failed += 1
            
            time.sleep(0.1)  # Krátka pauza
        
        print(f"\n🏁 === VÝSLEDOK ČISTENIA ===")
        print(f"✅ Úspešne presunuté do koša: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Zostáva v priečinku: {len(files_to_keep)} súborov")
        print(f"🧹 Priečinok je teraz vyčistený od duplikátov!")
        
        # Finálny prehľad
        print(f"\n📊 FINÁLNY STAV PRIEČINKA:")
        remaining_files = [f for f in os.listdir(directory) if f.lower().endswith('.txt')]
        remaining_files.sort()
        
        print(f"📄 Zostáva {len(remaining_files)} TXT súborov:")
        for i, file in enumerate(remaining_files[:15], 1):
            size = os.path.getsize(os.path.join(directory, file)) / 1024
            print(f"   {i:2d}. {file} ({size:.1f} KB)")
        
        if len(remaining_files) > 15:
            print(f"   ... a ďalších {len(remaining_files)-15} súborov")
    
    else:
        print("❌ Čistenie zrušené používateľom.")

def main():
    """Hlavná funkcia"""
    directory = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    cleanup_directory(directory)

if __name__ == "__main__":
    main()
