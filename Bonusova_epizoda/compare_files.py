#!/usr/bin/env python3
"""
Porovná počet audio súborov a textových prepisov
"""

import os

def compare_audio_and_text_files():
    """Porovná MP3 a TXT súbory"""
    
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    text_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("=== POROVNANIE AUDIO A TEXTOVÝCH SÚBOROV ===")
    print()
    
    # Získanie MP3 súborov
    mp3_files = []
    try:
        for file in os.listdir(audio_dir):
            if file.lower().endswith('.mp3'):
                mp3_files.append(file)
        mp3_files.sort()
    except Exception as e:
        print(f"✗ Chyba pri načítaní MP3 súborov: {e}")
        return
    
    # Získanie TXT súborov
    txt_files = []
    try:
        if os.path.exists(text_dir):
            for file in os.listdir(text_dir):
                if file.lower().endswith('.txt'):
                    txt_files.append(file)
            txt_files.sort()
        else:
            print(f"✗ Priečinok {text_dir} neexistuje")
            return
    except Exception as e:
        print(f"✗ Chyba pri načítaní TXT súborov: {e}")
        return
    
    print(f"📊 ŠTATISTIKY:")
    print(f"   MP3 súbory: {len(mp3_files)}")
    print(f"   TXT súbory: {len(txt_files)}")
    print(f"   Rozdiel: {len(mp3_files) - len(txt_files)}")
    print()
    
    # Vytvorenie zoznamov názvov bez prípon
    mp3_names = set()
    for mp3_file in mp3_files:
        name = os.path.splitext(mp3_file)[0]
        mp3_names.add(name)
    
    txt_names = set()
    for txt_file in txt_files:
        # Odstránenie rôznych prípon z TXT súborov
        name = txt_file
        # Odstránenie bežných prípon
        suffixes = ['_prepis.txt', '_info.txt', '_kompletny_prepis.txt', '.txt']
        for suffix in suffixes:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
                break
        txt_names.add(name)
    
    # Nájdenie chýbajúcich prepisov
    missing_transcripts = mp3_names - txt_names
    extra_transcripts = txt_names - mp3_names
    
    if missing_transcripts:
        print(f"🔍 EPIZÓDY BEZ PREPISU ({len(missing_transcripts)}):")
        for i, name in enumerate(sorted(missing_transcripts), 1):
            print(f"   {i:2d}. {name}")
        print()
    
    if extra_transcripts:
        print(f"📝 PREPISY BEZ AUDIO SÚBORU ({len(extra_transcripts)}):")
        for i, name in enumerate(sorted(extra_transcripts), 1):
            print(f"   {i:2d}. {name}")
        print()
    
    # Zhodné súbory
    matching = mp3_names & txt_names
    if matching:
        print(f"✅ EPIZÓDY S PREPISOM ({len(matching)}):")
        print(f"   (Zobrazujem prvých 10)")
        for i, name in enumerate(sorted(matching)[:10], 1):
            print(f"   {i:2d}. {name}")
        if len(matching) > 10:
            print(f"   ... a ďalších {len(matching)-10}")
        print()
    
    # Súhrn
    print("📋 SÚHRN:")
    print(f"   Celkom epizód: {len(mp3_names)}")
    print(f"   Prepísané: {len(matching)}")
    print(f"   Chýbajúce prepisy: {len(missing_transcripts)}")
    print(f"   Pokrytie: {len(matching)/len(mp3_names)*100:.1f}%")

def list_file_details():
    """Zobrazí detaily o súboroch"""
    
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    text_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("\n=== DETAILY SÚBOROV ===")
    
    # MP3 súbory
    print(f"\n📀 MP3 SÚBORY ({audio_dir}):")
    try:
        mp3_files = [f for f in os.listdir(audio_dir) if f.lower().endswith('.mp3')]
        mp3_files.sort()
        
        total_size = 0
        for i, file in enumerate(mp3_files[:10], 1):
            file_path = os.path.join(audio_dir, file)
            size_mb = os.path.getsize(file_path) / (1024*1024)
            total_size += size_mb
            print(f"   {i:2d}. {file} ({size_mb:.1f} MB)")
        
        if len(mp3_files) > 10:
            # Spočítanie celkovej veľkosti všetkých súborov
            for file in mp3_files[10:]:
                file_path = os.path.join(audio_dir, file)
                total_size += os.path.getsize(file_path) / (1024*1024)
            print(f"   ... a ďalších {len(mp3_files)-10} súborov")
        
        print(f"   Celková veľkosť: {total_size:.1f} MB ({total_size/1024:.2f} GB)")
        
    except Exception as e:
        print(f"   ✗ Chyba: {e}")
    
    # TXT súbory
    print(f"\n📝 TXT SÚBORY ({text_dir}):")
    try:
        if os.path.exists(text_dir):
            txt_files = [f for f in os.listdir(text_dir) if f.lower().endswith('.txt')]
            txt_files.sort()
            
            total_size = 0
            for i, file in enumerate(txt_files[:10], 1):
                file_path = os.path.join(text_dir, file)
                size_kb = os.path.getsize(file_path) / 1024
                total_size += size_kb
                print(f"   {i:2d}. {file} ({size_kb:.1f} KB)")
            
            if len(txt_files) > 10:
                # Spočítanie celkovej veľkosti všetkých súborov
                for file in txt_files[10:]:
                    file_path = os.path.join(text_dir, file)
                    total_size += os.path.getsize(file_path) / 1024
                print(f"   ... a ďalších {len(txt_files)-10} súborov")
            
            print(f"   Celková veľkosť: {total_size:.1f} KB ({total_size/1024:.2f} MB)")
        else:
            print("   Priečinok neexistuje")
            
    except Exception as e:
        print(f"   ✗ Chyba: {e}")

def main():
    """Hlavná funkcia"""
    compare_audio_and_text_files()
    list_file_details()

if __name__ == "__main__":
    main()
