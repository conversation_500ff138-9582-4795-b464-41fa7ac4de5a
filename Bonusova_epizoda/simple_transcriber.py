#!/usr/bin/env python3
"""
Jednoduchý skript na prepísanie textu z audio súborov bez ffmpeg
Používa len základné knižnice
"""

import os
import speech_recognition as sr
import time
from pathlib import Path

def setup_recognizer():
    """Nastavenie rozpoznávača reči"""
    r = sr.Recognizer()
    # Nastavenie pre lepšie rozpoznávanie slovenčiny
    r.energy_threshold = 300
    r.dynamic_energy_threshold = True
    r.pause_threshold = 0.8
    r.phrase_threshold = 0.3
    return r

def transcribe_wav_file(wav_file_path, output_dir):
    """Prepíše WAV súbor"""
    print(f"\nSpracovávam: {os.path.basename(wav_file_path)}")
    
    try:
        recognizer = setup_recognizer()
        
        # Načítanie audio súboru
        with sr.AudioFile(wav_file_path) as source:
            # Úprava pre hluk v pozadí
            print("Upravujem pre hluk v pozadí...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            
            # Načítanie celého audio súboru
            print("Načítavam audio...")
            audio_data = recognizer.record(source)
        
        print("Rozpoznávam reč...")
        
        # Prepis textu - skúsime rôzne jazyky
        transcript = None
        languages = ['sk-SK', 'cs-CZ', 'en-US']
        
        for lang in languages:
            try:
                print(f"Skúšam jazyk: {lang}")
                transcript = recognizer.recognize_google(audio_data, language=lang)
                print(f"✓ Úspešne rozpoznané v jazyku {lang}")
                break
            except sr.UnknownValueError:
                print(f"✗ Nerozpoznané v jazyku {lang}")
                continue
            except sr.RequestError as e:
                print(f"✗ Chyba služby pre jazyk {lang}: {e}")
                continue
        
        if not transcript:
            transcript = "[Nepodarilo sa rozpoznať reč v žiadnom jazyku]"
        
        # Vytvorenie názvu výstupného súboru
        base_name = os.path.splitext(os.path.basename(wav_file_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_prepis.txt")
        
        # Uloženie prepisu
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"PREPIS EPIZÓDY: {base_name}\n")
            f.write("=" * 50 + "\n\n")
            f.write(transcript)
            f.write(f"\n\n--- Koniec prepisu ---\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✓ Prepis uložený: {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní {wav_file_path}: {e}")
        return False

def convert_mp3_to_wav_simple(mp3_file_path):
    """Pokus o konverziu MP3 na WAV pomocou systémových nástrojov"""
    try:
        wav_path = mp3_file_path.replace('.mp3', '_converted.wav')
        
        # Skúsime použiť afconvert (macOS nástroj)
        import subprocess
        result = subprocess.run([
            'afconvert', 
            '-f', 'WAVE', 
            '-d', 'LEI16@16000',  # 16-bit little endian, 16kHz
            mp3_file_path, 
            wav_path
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(wav_path):
            print(f"✓ Konvertované na WAV: {wav_path}")
            return wav_path
        else:
            print(f"✗ Konverzia zlyhala: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"✗ Chyba pri konverzii: {e}")
        return None

def transcribe_mp3_file(mp3_file_path, output_dir):
    """Prepíše MP3 súbor (najprv ho konvertuje na WAV)"""
    print(f"\nSpracovávam MP3: {os.path.basename(mp3_file_path)}")
    
    # Pokus o konverziu na WAV
    wav_path = convert_mp3_to_wav_simple(mp3_file_path)
    
    if not wav_path:
        print("✗ Nepodarilo sa konvertovať MP3 na WAV")
        return False
    
    try:
        # Prepísanie WAV súboru
        success = transcribe_wav_file(wav_path, output_dir)
        
        # Vymazanie dočasného WAV súboru
        if os.path.exists(wav_path):
            os.remove(wav_path)
            print(f"Vymazaný dočasný súbor: {wav_path}")
        
        return success
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní: {e}")
        
        # Vymazanie dočasného WAV súboru
        if wav_path and os.path.exists(wav_path):
            os.remove(wav_path)
        
        return False

def main():
    """Hlavná funkcia"""
    # Cesty
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    # Vytvorenie adresára pre prepisy
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== Jednoduchý prepísač audio súborov Krvavý Dobšinský ===")
    print(f"Zdrojový adresár: {audio_dir}")
    print(f"Výstupný adresár: {output_dir}")
    print()
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    for file in os.listdir(audio_dir):
        if file.endswith('.mp3'):
            audio_files.append(os.path.join(audio_dir, file))
    
    if not audio_files:
        print("Nenašli sa žiadne MP3 súbory!")
        return
    
    print(f"Nájdených {len(audio_files)} audio súborov")
    
    # Spýtanie sa používateľa, ktoré súbory chce prepísať
    print("\nMôžete:")
    print("1. Prepísať všetky súbory")
    print("2. Prepísať len jeden konkrétny súbor")
    print("3. Prepísať prvých 3 súborov (test)")
    
    choice = input("\nVyberte možnosť (1/2/3): ").strip()
    
    files_to_process = []
    
    if choice == "1":
        files_to_process = audio_files
    elif choice == "2":
        print("\nDostupné súbory:")
        for i, file in enumerate(audio_files[:10]):  # Zobrazí prvých 10
            print(f"{i+1}. {os.path.basename(file)}")
        
        try:
            file_index = int(input("Vyberte číslo súboru: ")) - 1
            if 0 <= file_index < len(audio_files):
                files_to_process = [audio_files[file_index]]
            else:
                print("Neplatné číslo!")
                return
        except ValueError:
            print("Neplatný vstup!")
            return
    elif choice == "3":
        files_to_process = audio_files[:3]
    else:
        print("Neplatná voľba!")
        return
    
    # Spracovanie súborov
    successful = 0
    failed = 0
    
    for audio_file in files_to_process:
        success = transcribe_mp3_file(audio_file, output_dir)
        
        if success:
            successful += 1
        else:
            failed += 1
        
        # Pauza medzi súbormi
        print("Pauza 3 sekundy...")
        time.sleep(3)
    
    print(f"\n\nHotovo!")
    print(f"Úspešne prepísané: {successful}")
    print(f"Neúspešné: {failed}")
    print(f"Prepisy sú uložené v: {output_dir}")

if __name__ == "__main__":
    main()
