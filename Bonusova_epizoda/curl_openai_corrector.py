#!/usr/bin/env python3
"""
OpenAI korektor používajúci curl namiesto requests
"""

import os
import time
import json
import subprocess
import tempfile

class CurlOpenAICorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_remaining_episodes(self, max_count=20):
        """Získa zostávajúce epizódy na opravu"""
        if not os.path.exists(self.prepisy_dir):
            return []
        
        # Nájdenie všetkých _cleaned.txt súborov
        cleaned_files = []
        for file in os.listdir(self.prepisy_dir):
            if file.endswith('_cleaned.txt'):
                cleaned_files.append(file)
        
        # Nájdenie už opravených súborov
        corrected_episodes = set()
        if os.path.exists(self.corrected_dir):
            for file in os.listdir(self.corrected_dir):
                if file.endswith('_OpenAI_corrected.txt'):
                    base_name = file.replace('_OpenAI_corrected.txt', '')
                    corrected_episodes.add(f"{base_name}_cleaned.txt")
        
        # Zostávajúce súbory na opravu
        remaining_episodes = []
        for file in sorted(cleaned_files):
            if file not in corrected_episodes:
                base_name = file.replace('_cleaned.txt', '')
                remaining_episodes.append({
                    'base_name': base_name,
                    'file_name': file,
                    'file_path': os.path.join(self.prepisy_dir, file)
                })
                
                if len(remaining_episodes) >= max_count:
                    break
        
        return remaining_episodes
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai_curl(self, text, episode_name):
        """Opraví text pomocou OpenAI API cez curl"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 6000:
                text = text[:6000] + "\n\n[Text skrátený]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský":

1. Oprav gramatiku a preklepy
2. Oprav diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - slovenské úvodzovky „" a pomlčky –
4. Rozdeľ na logické odseky
5. Odstráň výplňové slová (ehm, no, tak, teda, atď.)
6. Zachovaj obsah, štýl a atmosféru
7. Vráť len opravený text

EPIZÓDA: {episode_name}

TEXT:
{text}"""
            
            # Vytvorenie JSON payload
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk. Opravuješ texty z horor podcastu s maximálnou presnosťou."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 3000,
                "temperature": 0.2
            }
            
            # Uloženie payload do dočasného súboru
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(payload, f, ensure_ascii=False, indent=2)
                temp_file = f.name
            
            try:
                # Curl príkaz
                curl_cmd = [
                    'curl',
                    '-X', 'POST',
                    'https://api.openai.com/v1/chat/completions',
                    '-H', 'Content-Type: application/json',
                    '-H', f'Authorization: Bearer {self.api_key}',
                    '-d', f'@{temp_file}',
                    '--max-time', '60'
                ]
                
                result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=70)
                
                if result.returncode == 0:
                    response_data = json.loads(result.stdout)
                    
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        corrected_text = response_data['choices'][0]['message']['content'].strip()
                        return corrected_text
                    else:
                        print(f"❌ Neplatná odpoveď: {response_data}")
                        return None
                else:
                    print(f"❌ Curl chyba: {result.stderr}")
                    return None
                    
            finally:
                # Vymazanie dočasného súboru
                os.unlink(temp_file)
                
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, episode_info):
        """Uloží opravený text"""
        try:
            base_name = episode_info['base_name']
            output_file = os.path.join(self.corrected_dir, f"{base_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {base_name}",
                "=" * 60,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "Podcast: Krvavý Dobšinský",
                "",
                corrected_text,
                "",
                "",
                "=" * 60,
                "INFORMÁCIE:",
                f"- Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini (curl)",
                f"- Pôvodný súbor: {episode_info['file_name']}",
                f"- Dĺžka: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika",
                "- Jazyk: slovenčina (SK)"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_episodes(self):
        """Spracuje epizódy"""
        print("🚀 === CURL OPENAI KOREKTOR ===")
        print(f"🕐 Začiatok: {time.strftime('%H:%M:%S')}")
        print()
        
        # Aktuálny stav
        try:
            current_corrected = len([f for f in os.listdir(self.corrected_dir) 
                                   if f.endswith('_OpenAI_corrected.txt')])
            print(f"📊 Už opravených: {current_corrected} epizód")
        except:
            current_corrected = 0
        
        # Získanie epizód na spracovanie
        episodes = self.get_remaining_episodes(max_count=20)
        
        if not episodes:
            print("🎉 Všetky epizódy už sú opravené!")
            return
        
        print(f"🎯 Spracovávam {len(episodes)} epizód:")
        for i, ep in enumerate(episodes, 1):
            print(f"   {i:2d}. {ep['base_name']}")
        print()
        
        successful = 0
        failed = 0
        start_time = time.time()
        
        for i, episode in enumerate(episodes, 1):
            print(f"[{i:2d}/{len(episodes)}] {episode['base_name']}")
            
            # Extrakcia textu
            story_text = self.extract_story_text(episode['file_path'])
            if not story_text or len(story_text) < 50:
                print(f"   ❌ Neplatný text")
                failed += 1
                continue
            
            print(f"   📖 Text: {len(story_text)} znakov")
            
            # Oprava
            print(f"   🤖 OpenAI (curl)...")
            corrected_text = self.correct_text_with_openai_curl(story_text, episode['base_name'])
            if not corrected_text:
                print(f"   ❌ Oprava zlyhala")
                failed += 1
                continue
            
            print(f"   ✅ Opravené: {len(corrected_text)} znakov")
            
            # Uloženie
            output_file = self.save_corrected_text(corrected_text, episode)
            if output_file:
                print(f"   💾 Uložené: {os.path.basename(output_file)}")
                successful += 1
            else:
                print(f"   ❌ Uloženie zlyhalo")
                failed += 1
            
            # Štatistiky
            elapsed = time.time() - start_time
            if i > 0:
                avg_time = elapsed / i
                remaining_time = avg_time * (len(episodes) - i)
                print(f"   ⏱️  Čas: {elapsed/60:.1f}min | Zostáva: ~{remaining_time/60:.1f}min")
            
            # Pauza
            if i < len(episodes):
                print("   ⏳ Pauza 3s...")
                time.sleep(3)
            
            print()
        
        # Finálny súhrn
        total_time = time.time() - start_time
        final_corrected = current_corrected + successful
        
        print(f"🏁 === VÝSLEDOK DÁVKY ===")
        print(f"✅ Úspešne: {successful}")
        print(f"❌ Neúspešne: {failed}")
        print(f"📊 Celkom opravených: {final_corrected} epizód")
        print(f"⏱️  Čas: {total_time/60:.1f} minút")
        print(f"📁 Súbory: {self.corrected_dir}")

def main():
    """Hlavná funkcia"""
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = CurlOpenAICorrector(api_key)
    corrector.process_episodes()

if __name__ == "__main__":
    main()
