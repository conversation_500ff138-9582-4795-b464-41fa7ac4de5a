# Prepísanie podcastu Krvavý Dobšinský

## Čo sa už urobilo

### 1. Stiahnutie audio súborov ✅
- **Počet súborov**: 94 epizód
- **Umiestnenie**: `/Users/<USER>/Desktop/Krvavý Audio/`
- **Formát**: MP3
- **Názvy**: Podľa názvov epizód

### 2. Príprava na prepis ✅
- **Priečinok pre prepisy**: `/Users/<USER>/Desktop/Krvavý Audio/Prepisy/`
- **Informačné súbory**: Vytvorené pre prvých 5 epizód
- **Automatický skript**: `transcribe_all.py` pripravený

## Ako prepísať audio súbory

### Možnosť 1: Automatický prepis všetkých súborov
```bash
cd "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
python3 transcribe_all.py
```

**Poznámka**: Vyžaduje internetové pripojenie pre Google Speech Recognition

### Možnosť 2: Manuálny prepis jednotlivých súborov
```bash
cd "/Users/<USER>/Documents/augment-projects/Bonusova_epizoda"
python3 simple_transcriber.py
```

### Možnosť 3: Offline informácie o súboroch
```bash
cd "/Users/<USER>/Documents/augment-projects/Bonusova_epizoda"
python3 offline_transcriber.py
```

## Štruktúra súborov

```
Desktop/
└── Krvavý Audio/
    ├── [94 MP3 súborov s názvami epizód]
    └── Prepisy/
        ├── transcribe_all.py          # Automatický skript
        ├── [názov epizódy]_info.txt   # Informácie o súbore
        └── [názov epizódy]_prepis.txt # Prepis textu (po spustení)
```

## Technické požiadavky

### Nainštalované knižnice ✅
- `SpeechRecognition` - rozpoznávanie reči
- `pydub` - spracovanie audio
- `requests` - sťahovanie súborov

### Systémové nástroje (macOS) ✅
- `afconvert` - konverzia audio formátov
- `afinfo` - informácie o audio súboroch

## Príklady názvov epizód

1. Tichá voda
2. Posledné vysielanie
3. Prišli v maskách
4. Horor v čase korony
5. Skutočný príbeh srbských upírov (1725-1731)
6. Zmok (bez kostola a umývania) časť 1.
7. Pustý hrad
... a ďalších 87 epizód

## Riešenie problémov

### Problém s internetovým pripojením
- Skript `transcribe_all.py` vyžaduje pripojenie na internet
- Ak nie je k dispozícii, použite `offline_transcriber.py` pre základné informácie

### Problém s konverziou audio
- Skript používa macOS nástroj `afconvert`
- Ak nefunguje, možno bude potrebné nainštalovať `ffmpeg`

### Problém s rozpoznávaním slovenčiny
- Skript automaticky skúša slovenčinu (sk-SK), češtinu (cs-CZ) a angličtinu (en-US)
- Google Speech Recognition má obmedzenia na dĺžku audio súborov

## Odporúčania

1. **Pre najlepšie výsledky**: Spustite `transcribe_all.py` s dobrým internetovým pripojením
2. **Pre rýchly test**: Použite možnosť prepísania len prvých 3-5 súborov
3. **Pre offline prácu**: Použite `offline_transcriber.py` na získanie informácií o súboroch

## Kontakt

Ak máte problémy alebo otázky, kontaktujte vývojára skriptov.

---
*Vytvorené: 2025-01-27*
*Verzia: 1.0*
