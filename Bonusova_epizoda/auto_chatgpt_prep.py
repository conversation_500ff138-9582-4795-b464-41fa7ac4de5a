#!/usr/bin/env python3
"""
Automaticky pripraví text pre ChatGPT opravu
"""

import os
import time
import subprocess
import pyperclip

def get_first_text_file():
    """Vyberie prvý dostupný textový súbor"""
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    if not os.path.exists(prepisy_dir):
        return None
    
    # Nájdenie prvého _cleaned.txt súboru
    for file in sorted(os.listdir(prepisy_dir)):
        if file.endswith('_cleaned.txt'):
            return os.path.join(prepisy_dir, file)
    
    return None

def extract_story_text(file_path):
    """Extrahuje samotný príbeh z textového súboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        story_start = 0
        story_end = len(lines)
        
        # Nájdenie začiatku príbehu
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                story_start = i + 1
                break
        
        # Nájdenie konca príbehu
        for i in range(len(lines)-1, -1, -1):
            if any(keyword in line.lower() for keyword in 
                   ['informácie', 'spracované:', 'koniec', '===']):
                story_end = i
                break
        
        story_lines = lines[story_start:story_end]
        story_text = '\n'.join(story_lines).strip()
        
        return story_text
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní súboru: {e}")
        return None

def create_chatgpt_prompt(text, file_name):
    """Vytvorí prompt pre ChatGPT"""
    
    # Skrátenie textu ak je príliš dlhý
    if len(text) > 6000:
        text = text[:6000] + "\n\n[Text skrátený kvôli limitom ChatGPT]"
    
    prompt = f"""Oprav tento slovenský text z podcastu "Krvavý Dobšinský". 

ÚLOHA:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu (úvodzovky „", pomlčky –, bodky, čiarky)
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Vráť len opravený text bez komentárov alebo vysvetlení

NÁZOV EPIZÓDY: {file_name}

TEXT NA OPRAVU:
{text}

OPRAVENÝ TEXT:"""

    return prompt

def open_chatgpt():
    """Otvorí ChatGPT aplikáciu"""
    try:
        print("🚀 Otváram ChatGPT aplikáciu...")
        
        # Pokus o otvorenie cez open command
        result = subprocess.run(['open', '-a', 'ChatGPT'], capture_output=True)
        
        if result.returncode == 0:
            print("✅ ChatGPT aplikácia otvorená")
            time.sleep(3)
            return True
        else:
            print("⚠️ ChatGPT aplikácia sa nenašla, otvorte ju manuálne")
            return True
            
    except Exception as e:
        print(f"❌ Chyba pri otváraní ChatGPT: {e}")
        print("💡 Otvorte ChatGPT manuálne")
        return True

def save_instructions(file_name, corrected_dir):
    """Uloží inštrukcie do súboru"""
    instructions_file = os.path.join(corrected_dir, f"INSTRUCTIONS_{file_name}.txt")
    
    instructions = f"""INŠTRUKCIE PRE OPRAVU TEXTU: {file_name}

1. PROMPT JE UŽ V SCHRÁNKE
   - Otvorte ChatGPT aplikáciu
   - Stlačte Cmd+V na vloženie prompt-u
   - Stlačte Enter na odoslanie

2. POČKAJTE NA ODPOVEĎ CHATGPT

3. SKOPÍRUJTE OPRAVENÝ TEXT
   - Označte celý opravený text
   - Stlačte Cmd+C na skopírovanie

4. ULOŽTE OPRAVENÝ TEXT
   - Vytvorte nový súbor: {file_name}_ChatGPT_corrected.txt
   - Uložte do priečinka: {corrected_dir}
   - Vložte opravený text (Cmd+V)

5. HOTOVO!

Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    return instructions_file

def main():
    """Hlavná funkcia"""
    print("🤖 === AUTOMATICKÁ PRÍPRAVA PRE CHATGPT ===")
    print()
    
    # Vytvorenie priečinka corrected
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    os.makedirs(corrected_dir, exist_ok=True)
    print(f"📁 Priečinok corrected: {corrected_dir}")
    
    # Výber súboru
    file_path = get_first_text_file()
    if not file_path:
        print("❌ Nenašiel sa žiadny súbor na opravu!")
        return
    
    file_name = os.path.basename(file_path).replace('_cleaned.txt', '')
    print(f"📄 Vybraný súbor: {file_name}")
    
    # Extrakcia textu
    print("📖 Načítavam text...")
    story_text = extract_story_text(file_path)
    if not story_text:
        print("❌ Nepodarilo sa extrahovať text!")
        return
    
    print(f"✅ Text načítaný ({len(story_text)} znakov)")
    
    # Vytvorenie prompt-u
    prompt = create_chatgpt_prompt(story_text, file_name)
    
    # Skopírovanie do schránky
    pyperclip.copy(prompt)
    print("📋 Prompt skopírovaný do schránky")
    
    # Uloženie inštrukcie
    instructions_file = save_instructions(file_name, corrected_dir)
    print(f"📝 Inštrukcie uložené: {os.path.basename(instructions_file)}")
    
    # Otvorenie ChatGPT
    open_chatgpt()
    
    print("\n" + "="*60)
    print("🎯 VŠETKO JE PRIPRAVENÉ!")
    print("="*60)
    print("✅ ChatGPT aplikácia je otvorená")
    print("✅ Prompt je v schránke")
    print("✅ Inštrukcie sú uložené")
    print()
    print("🔄 ĎALŠIE KROKY:")
    print("1. Prejdite do ChatGPT aplikácie")
    print("2. Stlačte Cmd+V (vložiť prompt)")
    print("3. Stlačte Enter (odoslať)")
    print("4. Počkajte na odpoveď")
    print("5. Skopírujte opravený text (Cmd+A, Cmd+C)")
    print("6. Uložte ako: " + f"{file_name}_ChatGPT_corrected.txt")
    print("7. Do priečinka: corrected")
    print("="*60)
    
    print(f"\n📖 SPRACOVÁVANÝ PRÍBEH: {file_name}")
    print(f"📊 Dĺžka textu: {len(story_text)} znakov")
    print(f"📁 Cieľový priečinok: {corrected_dir}")
    
    print(f"\n🎉 Môžete pokračovať v ChatGPT aplikácii!")

if __name__ == "__main__":
    main()
