#!/usr/bin/env python3
"""
Pripraví text pre ChatGPT opravu a otvorí ChatGPT aplikáciu
"""

import os
import time
import subprocess
import pyperclip

def get_text_file():
    """Vyberie textový súbor na opravu"""
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    if not os.path.exists(prepisy_dir):
        return None
    
    # Nájdenie všetkých _cleaned.txt súborov
    txt_files = []
    for file in os.listdir(prepisy_dir):
        if file.endswith('_cleaned.txt'):
            txt_files.append(os.path.join(prepisy_dir, file))
    
    if not txt_files:
        return None
    
    # Zobrazenie možností
    print("📋 Dostupné súbory na opravu:")
    for i, file_path in enumerate(txt_files[:10], 1):
        file_name = os.path.basename(file_path).replace('_cleaned.txt', '')
        file_size = os.path.getsize(file_path) / 1024
        print(f"   {i:2d}. {file_name} ({file_size:.1f} KB)")
    
    if len(txt_files) > 10:
        print(f"   ... a ďalších {len(txt_files)-10} súborov")
    
    try:
        choice = input(f"\nVyberte číslo súboru (1-{min(10, len(txt_files))}): ").strip()
        index = int(choice) - 1
        
        if 0 <= index < min(10, len(txt_files)):
            return txt_files[index]
        else:
            print("❌ Neplatné číslo!")
            return None
    except ValueError:
        print("❌ Neplatný vstup!")
        return None

def extract_story_text(file_path):
    """Extrahuje samotný príbeh z textového súboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        story_start = 0
        story_end = len(lines)
        
        # Nájdenie začiatku príbehu
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                story_start = i + 1
                break
        
        # Nájdenie konca príbehu
        for i in range(len(lines)-1, -1, -1):
            if any(keyword in line.lower() for keyword in 
                   ['informácie', 'spracované:', 'koniec', '===']):
                story_end = i
                break
        
        story_lines = lines[story_start:story_end]
        story_text = '\n'.join(story_lines).strip()
        
        return story_text
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní súboru: {e}")
        return None

def create_chatgpt_prompt(text, file_name):
    """Vytvorí prompt pre ChatGPT"""
    
    # Skrátenie textu ak je príliš dlhý
    if len(text) > 6000:
        text = text[:6000] + "\n\n[Text skrátený kvôli limitom ChatGPT]"
    
    prompt = f"""Oprav tento slovenský text z podcastu "Krvavý Dobšinský". 

ÚLOHA:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu (úvodzovky „", pomlčky –, bodky, čiarky)
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Vráť len opravený text bez komentárov alebo vysvetlení

NÁZOV EPIZÓDY: {file_name}

TEXT NA OPRAVU:
{text}

OPRAVENÝ TEXT:"""

    return prompt

def open_chatgpt():
    """Otvorí ChatGPT aplikáciu"""
    try:
        print("🚀 Otváram ChatGPT aplikáciu...")
        
        # Pokus o otvorenie cez open command
        result = subprocess.run(['open', '-a', 'ChatGPT'], capture_output=True)
        
        if result.returncode == 0:
            print("✅ ChatGPT aplikácia otvorená")
            time.sleep(3)
            return True
        else:
            # Pokus cez Spotlight
            os.system("open -a 'Spotlight Search'")
            time.sleep(1)
            print("🔍 Hľadám ChatGPT cez Spotlight...")
            return True
            
    except Exception as e:
        print(f"❌ Chyba pri otváraní ChatGPT: {e}")
        print("💡 Otvorte ChatGPT manuálne")
        return True

def main():
    """Hlavná funkcia"""
    print("🤖 === PRÍPRAVA TEXTU PRE CHATGPT ===")
    print()
    
    # Vytvorenie priečinka corrected
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    os.makedirs(corrected_dir, exist_ok=True)
    print(f"📁 Priečinok corrected: {corrected_dir}")
    print()
    
    # Výber súboru
    file_path = get_text_file()
    if not file_path:
        print("❌ Nebol vybraný žiadny súbor!")
        return
    
    file_name = os.path.basename(file_path).replace('_cleaned.txt', '')
    print(f"\n📄 Vybraný súbor: {file_name}")
    
    # Extrakcia textu
    print("📖 Načítavam text...")
    story_text = extract_story_text(file_path)
    if not story_text:
        print("❌ Nepodarilo sa extrahovať text!")
        return
    
    print(f"✅ Text načítaný ({len(story_text)} znakov)")
    
    # Vytvorenie prompt-u
    prompt = create_chatgpt_prompt(story_text, file_name)
    
    # Skopírovanie do schránky
    pyperclip.copy(prompt)
    print("📋 Prompt skopírovaný do schránky")
    
    # Otvorenie ChatGPT
    open_chatgpt()
    
    print("\n" + "="*60)
    print("🎯 ĎALŠIE KROKY:")
    print("1. ✅ ChatGPT aplikácia je otvorená")
    print("2. ✅ Prompt je v schránke (Cmd+V na vloženie)")
    print("3. 📝 Vložte prompt do ChatGPT a stlačte Enter")
    print("4. ⏳ Počkajte na odpoveď ChatGPT")
    print("5. 📋 Skopírujte opravený text (Cmd+A, Cmd+C)")
    print("6. 💾 Uložte text do súboru v priečinku 'corrected'")
    print("="*60)
    
    print(f"\n📝 NÁZOV SÚBORU NA ULOŽENIE:")
    print(f"{file_name}_ChatGPT_corrected.txt")
    
    print(f"\n📁 ULOŽIŤ DO:")
    print(f"{corrected_dir}")
    
    print(f"\n📖 UKÁŽKA PROMPT-U:")
    print("-" * 50)
    preview = prompt[:300] + "..." if len(prompt) > 300 else prompt
    print(preview)
    print("-" * 50)
    
    print(f"\n🎉 Všetko je pripravené! Pokračujte v ChatGPT aplikácii.")

if __name__ == "__main__":
    main()
