#!/usr/bin/env python3
"""
Oprava všetkých zostávajúcich súborov (aj tých bez _cleaned.txt)
"""

import os
import time
import json
import subprocess
import tempfile

class FinalRemainingCorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_all_remaining_episodes(self):
        """Získa všetky zostávajúce epizódy na opravu (aj bez _cleaned.txt)"""
        if not os.path.exists(self.prepisy_dir):
            return []
        
        # Nájdenie všetkých TXT súborov
        all_txt_files = []
        for file in os.listdir(self.prepisy_dir):
            if file.endswith('.txt') and not any(skip in file.lower() for skip in [
                'instructions', 'test', 'backup', 'temp', 'openai', 'chatgpt', 'corrected'
            ]):
                all_txt_files.append(file)
        
        # Nájdenie už opravených súborov
        corrected_episodes = set()
        if os.path.exists(self.corrected_dir):
            for file in os.listdir(self.corrected_dir):
                if file.endswith('_OpenAI_corrected.txt'):
                    base_name = file.replace('_OpenAI_corrected.txt', '')
                    # Pridanie rôznych možných formátov
                    corrected_episodes.add(f"{base_name}_cleaned.txt")
                    corrected_episodes.add(f"{base_name}.txt")
                    corrected_episodes.add(f"{base_name}_prepis.txt")
                    corrected_episodes.add(f"{base_name}_kompletny_prepis.txt")
        
        # Zostávajúce súbory na opravu
        remaining_episodes = []
        for file in sorted(all_txt_files):
            if file not in corrected_episodes:
                # Určenie základného názvu
                base_name = file
                for suffix in ['_cleaned.txt', '_prepis.txt', '_kompletny_prepis.txt', '.txt']:
                    if base_name.endswith(suffix):
                        base_name = base_name[:-len(suffix)]
                        break
                
                remaining_episodes.append({
                    'base_name': base_name,
                    'file_name': file,
                    'file_path': os.path.join(self.prepisy_dir, file)
                })
        
        return remaining_episodes
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai_curl(self, text, episode_name):
        """Opraví text pomocou OpenAI API cez curl"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 6000:
                text = text[:6000] + "\n\n[Text skrátený]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský":

1. Oprav gramatiku a preklepy
2. Oprav diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - slovenské úvodzovky „" a pomlčky –
4. Rozdeľ na logické odseky
5. Odstráň výplňové slová (ehm, no, tak, teda, atď.)
6. Zachovaj obsah, štýl a atmosféru
7. Vráť len opravený text

EPIZÓDA: {episode_name}

TEXT:
{text}"""
            
            # Vytvorenie JSON payload
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk. Opravuješ texty z horor podcastu s maximálnou presnosťou."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 3000,
                "temperature": 0.2
            }
            
            # Uloženie payload do dočasného súboru
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(payload, f, ensure_ascii=False, indent=2)
                temp_file = f.name
            
            try:
                # Curl príkaz
                curl_cmd = [
                    'curl',
                    '-X', 'POST',
                    'https://api.openai.com/v1/chat/completions',
                    '-H', 'Content-Type: application/json',
                    '-H', f'Authorization: Bearer {self.api_key}',
                    '-d', f'@{temp_file}',
                    '--max-time', '60'
                ]
                
                result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=70)
                
                if result.returncode == 0:
                    response_data = json.loads(result.stdout)
                    
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        corrected_text = response_data['choices'][0]['message']['content'].strip()
                        return corrected_text
                    else:
                        print(f"❌ Neplatná odpoveď: {response_data}")
                        return None
                else:
                    print(f"❌ Curl chyba: {result.stderr}")
                    return None
                    
            finally:
                # Vymazanie dočasného súboru
                os.unlink(temp_file)
                
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, episode_info):
        """Uloží opravený text"""
        try:
            base_name = episode_info['base_name']
            output_file = os.path.join(self.corrected_dir, f"{base_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {base_name}",
                "=" * 60,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "Podcast: Krvavý Dobšinský",
                "",
                corrected_text,
                "",
                "",
                "=" * 60,
                "INFORMÁCIE:",
                f"- Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini (curl)",
                f"- Pôvodný súbor: {episode_info['file_name']}",
                f"- Dĺžka: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika",
                "- Jazyk: slovenčina (SK)"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_all_remaining(self):
        """Spracuje všetky zostávajúce epizódy"""
        print("🚀 === FINÁLNA OPRAVA VŠETKÝCH ZOSTÁVAJÚCICH EPIZÓD ===")
        print(f"🕐 Začiatok: {time.strftime('%H:%M:%S')}")
        print()
        
        # Aktuálny stav
        try:
            current_corrected = len([f for f in os.listdir(self.corrected_dir) 
                                   if f.endswith('_OpenAI_corrected.txt')])
            print(f"📊 Už opravených: {current_corrected} epizód")
        except:
            current_corrected = 0
        
        # Získanie všetkých zostávajúcich epizód
        remaining_episodes = self.get_all_remaining_episodes()
        
        if not remaining_episodes:
            print("🎉 Všetky epizódy už sú opravené!")
            return
        
        print(f"🎯 Zostáva na opravu: {len(remaining_episodes)} epizód")
        print()
        
        # Zobrazenie všetkých epizód
        print("📋 Epizódy na spracovanie:")
        for i, ep in enumerate(remaining_episodes, 1):
            print(f"   {i:2d}. {ep['base_name']} ({ep['file_name']})")
        print()
        
        successful = 0
        failed = 0
        start_time = time.time()
        
        for i, episode in enumerate(remaining_episodes, 1):
            print(f"[{i:2d}/{len(remaining_episodes)}] {episode['base_name']}")
            
            # Extrakcia textu
            story_text = self.extract_story_text(episode['file_path'])
            if not story_text or len(story_text) < 50:
                print(f"   ❌ Neplatný text ({len(story_text) if story_text else 0} znakov)")
                failed += 1
                continue
            
            print(f"   📖 Text: {len(story_text)} znakov")
            
            # Oprava
            print(f"   🤖 OpenAI (curl)...")
            corrected_text = self.correct_text_with_openai_curl(story_text, episode['base_name'])
            if not corrected_text:
                print(f"   ❌ Oprava zlyhala")
                failed += 1
                continue
            
            print(f"   ✅ Opravené: {len(corrected_text)} znakov")
            
            # Uloženie
            output_file = self.save_corrected_text(corrected_text, episode)
            if output_file:
                print(f"   💾 Uložené: {os.path.basename(output_file)}")
                successful += 1
            else:
                print(f"   ❌ Uloženie zlyhalo")
                failed += 1
            
            # Štatistiky
            elapsed = time.time() - start_time
            if i > 0:
                avg_time = elapsed / i
                remaining_time = avg_time * (len(remaining_episodes) - i)
                print(f"   ⏱️  Čas: {elapsed/60:.1f}min | Zostáva: ~{remaining_time/60:.1f}min")
            
            # Pauza
            if i < len(remaining_episodes):
                print("   ⏳ Pauza 3s...")
                time.sleep(3)
            
            print()
        
        # Finálny súhrn
        total_time = time.time() - start_time
        final_corrected = current_corrected + successful
        
        print(f"🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne opravené: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📊 CELKOM OPRAVENÝCH EPIZÓD: {final_corrected}")
        print(f"⏱️  Čas: {total_time/60:.1f} minút")
        print(f"📁 Súbory: {self.corrected_dir}")
        
        if successful == len(remaining_episodes):
            print(f"\n🎉🎉🎉 PROJEKT DOKONČENÝ! 🎉🎉🎉")
            print(f"📚 Všetky epizódy sú v profesionálnej slovenčine!")
            print(f"🏆 100% kvalita cez OpenAI GPT-4o-mini")

def main():
    """Hlavná funkcia"""
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = FinalRemainingCorrector(api_key)
    corrector.process_all_remaining()

if __name__ == "__main__":
    main()
