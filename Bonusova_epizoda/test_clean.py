#!/usr/bin/env python3
"""
Test čistenia súborov
"""

import os

def test_clean():
    """Test čistenia"""
    
    # Kontrola priečinkov
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    corrected_dir = os.path.join(base_dir, "corrected")
    clean_dir = os.path.join(base_dir, "clean_stories")
    
    print("🔍 Test čistenia súborov")
    print(f"Základný priečinok: {base_dir}")
    print(f"Existuje: {os.path.exists(base_dir)}")
    print()
    
    print(f"Corrected priečinok: {corrected_dir}")
    print(f"Existuje: {os.path.exists(corrected_dir)}")
    if os.path.exists(corrected_dir):
        files = [f for f in os.listdir(corrected_dir) if f.endswith('_OpenAI_corrected.txt')]
        print(f"OpenAI súbory: {len(files)}")
    print()
    
    print(f"Clean priečinok: {clean_dir}")
    print(f"Existuje: {os.path.exists(clean_dir)}")
    if os.path.exists(clean_dir):
        files = os.listdir(clean_dir)
        print(f"Súbory: {len(files)}")
        if files:
            print("Prvých 5:")
            for file in files[:5]:
                print(f"  - {file}")
    else:
        print("❌ Clean priečinok neexistuje - vytváram ho")
        os.makedirs(clean_dir, exist_ok=True)
        print(f"✅ Vytvorený: {clean_dir}")
    
    # Test jedného súboru
    if os.path.exists(corrected_dir):
        test_files = [f for f in os.listdir(corrected_dir) if f.endswith('_OpenAI_corrected.txt')]
        if test_files:
            test_file = test_files[0]
            test_path = os.path.join(corrected_dir, test_file)
            
            print(f"\n📄 Test súboru: {test_file}")
            
            try:
                with open(test_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"Dĺžka: {len(content)} znakov")
                print("Prvých 200 znakov:")
                print(content[:200])
                print("...")
                
                # Jednoduchá extrakcia
                lines = content.split('\n')
                clean_lines = []
                
                for line in lines:
                    if not any(skip in line for skip in [
                        'EPIZÓDA:', 'Opravené:', 'Kvalita:', 'INFORMÁCIE:', '==='
                    ]):
                        if line.strip():
                            clean_lines.append(line)
                
                clean_text = '\n'.join(clean_lines).strip()
                print(f"\nVyčistený text ({len(clean_text)} znakov):")
                print(clean_text[:200])
                
                # Uloženie testu
                episode_name = test_file.replace('_OpenAI_corrected.txt', '')
                clean_file_path = os.path.join(clean_dir, f"{episode_name}.txt")
                
                with open(clean_file_path, 'w', encoding='utf-8') as f:
                    f.write(clean_text)
                
                print(f"\n✅ Test súbor uložený: {episode_name}.txt")
                
            except Exception as e:
                print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    test_clean()
