#!/usr/bin/env python3
"""
Pokročilý korektor slovenských textov s dôrazom na perfektný pravopis a gramatiku
"""

import os
import re
import time

class SlovakTextCorrector:
    def __init__(self):
        # Slovensk<PERSON> diakritické znaky
        self.slovak_chars = {
            'a': 'á', 'e': 'é', 'i': 'í', 'o': 'ó', 'u': 'ú', 'y': 'ý',
            'c': 'č', 'd': 'ď', 'l': 'ľ', 'n': 'ň', 'r': 'ř', 's': 'š', 't': 'ť', 'z': 'ž'
        }
        
        # Časté slovenské slová s diakritikoy
        self.slovak_words = {
            # Základné slová
            'a': 'a', 'ale': 'ale', 'ako': 'ako', 'alebo': 'alebo',
            'ano': 'áno', 'az': 'až', 'bolo': 'bolo', 'bude': 'bude',
            'co': 'čo', 'ci': 'či', 'cize': 'čiže', 'cas': 'čas',
            'dalej': 'ďalej', 'dalsi': 'ďalší', 'dalsia': 'ďalšia', 'dalsie': 'ďalšie',
            'este': 'ešte', 'jeden': 'jeden', 'jedna': 'jedna', 'jedno': 'jedno',
            'jeho': 'jeho', 'jej': 'jej', 'ich': 'ich', 'im': 'im',
            'kde': 'kde', 'ked': 'keď', 'kedze': 'keďže', 'ktory': 'ktorý',
            'ktora': 'ktorá', 'ktore': 'ktoré', 'kto': 'kto',
            'len': 'len', 'lebo': 'lebo', 'ma': 'má', 'mam': 'mám',
            'nas': 'náš', 'nasa': 'naša', 'nase': 'naše', 'nie': 'nie',
            'nic': 'nič', 'nikto': 'nikto', 'niekedy': 'niekedy',
            'potom': 'potom', 'preco': 'prečo', 'pred': 'pred', 'pri': 'pri',
            'sa': 'sa', 'si': 'si', 'som': 'som', 'su': 'sú', 'sme': 'sme',
            'tak': 'tak', 'takze': 'takže', 'tam': 'tam', 'tu': 'tu',
            'uz': 'už', 'vas': 'váš', 'vasa': 'vaša', 'vase': 'vaše',
            'viac': 'viac', 'vsak': 'však', 'vsetko': 'všetko', 'vsetci': 'všetci',
            'ze': 'že', 'zeby': 'žeby', 'ziaden': 'žiaden', 'ziadna': 'žiadna',
            
            # Časové výrazy
            'dnes': 'dnes', 'vcera': 'včera', 'zajtra': 'zajtra',
            'rano': 'ráno', 'vecer': 'večer', 'noc': 'noc',
            'rok': 'rok', 'roky': 'roky', 'mesiac': 'mesiac',
            'tyzden': 'týždeň', 'den': 'deň', 'dni': 'dni',
            
            # Miesta
            'slovensko': 'Slovensko', 'bratislava': 'Bratislava',
            'kosice': 'Košice', 'presov': 'Prešov', 'zilina': 'Žilina',
            'banska bystrica': 'Banská Bystrica', 'trencin': 'Trenčín',
            'nitra': 'Nitra', 'trnava': 'Trnava',
            
            # Podcast špecifické
            'krvavý': 'Krvavý', 'dobšinský': 'Dobšinský', 'dobsinsky': 'Dobšinský',
            'podcast': 'podcast', 'epizoda': 'epizóda', 'epizody': 'epizódy',
            'pribeh': 'príbeh', 'pribehy': 'príbehy', 'rozprávka': 'rozprávka',
            'horor': 'horor', 'horory': 'horory', 'strašidelny': 'strašidelný',
            'duchovia': 'duchovia', 'duch': 'duch', 'upir': 'upír',
            'upiry': 'upíry', 'vampir': 'vampír', 'vampiry': 'vampíry',
            'hrad': 'hrad', 'hrady': 'hrady', 'kastiel': 'kaštieľ',
            'les': 'les', 'lesy': 'lesy', 'hora': 'hora', 'hory': 'hory',
            'dedina': 'dedina', 'dediny': 'dediny', 'mesto': 'mesto',
            
            # Emócie a pocity
            'strach': 'strach', 'hroza': 'hrôza', 'desivý': 'desivý',
            'desiva': 'desivá', 'desive': 'desivé', 'tajomny': 'tajomný',
            'tajomna': 'tajomná', 'tajomne': 'tajomné', 'temny': 'temný',
            'temna': 'temná', 'temne': 'temné', 'smutny': 'smutný',
            'smutna': 'smutná', 'smutne': 'smutné', 'vesely': 'veselý',
            'vesela': 'veselá', 'vesele': 'veselé'
        }
        
        # Časté gramatické chyby
        self.grammar_fixes = {
            # Predložky
            ' v ': ' v ', ' s ': ' s ', ' z ': ' z ', ' k ': ' k ',
            ' o ': ' o ', ' na ': ' na ', ' za ': ' za ', ' pre ': ' pre ',
            ' pri ': ' pri ', ' po ': ' po ', ' od ': ' od ', ' do ': ' do ',
            
            # Spojky
            ' a ': ' a ', ' ale ': ' ale ', ' lebo ': ' lebo ',
            ' ze ': ' že ', ' ked ': ' keď ', ' kedze ': ' keďže ',
            
            # Zámená
            ' sa ': ' sa ', ' si ': ' si ', ' ho ': ' ho ', ' ju ': ' ju ',
            ' ich ': ' ich ', ' im ': ' im ', ' mu ': ' mu ', ' jej ': ' jej ',
            
            # Častice
            ' uz ': ' už ', ' este ': ' ešte ', ' len ': ' len ',
            ' az ': ' až ', ' ani ': ' ani ', ' ani nie ': ' ani nie ',
        }

    def fix_diacritics(self, text):
        """Opraví diakritiku v slovenských slovách"""
        words = text.split()
        corrected_words = []
        
        for word in words:
            # Odstránenie interpunkcie pre kontrolu
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            # Kontrola či slovo existuje v slovníku
            if clean_word in self.slovak_words:
                # Zachovanie pôvodnej interpunkcie
                corrected = word
                for char in word:
                    if char.lower() == clean_word:
                        corrected = word.replace(clean_word, self.slovak_words[clean_word], 1)
                        break
                corrected_words.append(corrected)
            else:
                corrected_words.append(word)
        
        return ' '.join(corrected_words)

    def fix_punctuation_advanced(self, text):
        """Pokročilá oprava interpunkcie pre slovenčinu"""
        # Odstránenie medzier pred interpunkciou
        text = re.sub(r'\s+([,.!?:;])', r'\1', text)
        
        # Pridanie medzery za interpunkciu
        text = re.sub(r'([,.!?:;])([a-zA-ZáäčďéíĺľňóôŕšťúýžÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ])', r'\1 \2', text)
        
        # Oprava úvodzoviek
        text = re.sub(r'"\s*([^"]*?)\s*"', r'„\1"', text)
        
        # Oprava pomlčiek
        text = re.sub(r'\s*-\s*', ' – ', text)
        text = re.sub(r'–\s*–', '–', text)
        
        # Oprava viacnásobnej interpunkcie
        text = re.sub(r'[,]{2,}', ',', text)
        text = re.sub(r'[.]{2,}', '…', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        return text

    def fix_capitalization_slovak(self, text):
        """Opraví veľké písmená podľa slovenských pravidiel"""
        # Veľké písmeno na začiatku textu
        text = re.sub(r'^([a-záäčďéíĺľňóôŕšťúýž])', lambda m: m.group(1).upper(), text)
        
        # Veľké písmeno po bodke, výkričníku, otázníku
        text = re.sub(r'([.!?]\s+)([a-záäčďéíĺľňóôŕšťúýž])', 
                     lambda m: m.group(1) + m.group(2).upper(), text)
        
        # Veľké písmeno po dvojbodke ak nasleduje priama reč
        text = re.sub(r'(:\s*[„"])([a-záäčďéíĺľňóôŕšťúýž])', 
                     lambda m: m.group(1) + m.group(2).upper(), text)
        
        # Vlastné mená miest
        for city in ['bratislava', 'košice', 'prešov', 'žilina', 'banská bystrica', 
                     'trenčín', 'nitra', 'trnava', 'slovensko']:
            text = re.sub(r'\b' + city + r'\b', city.title(), text, flags=re.IGNORECASE)
        
        return text

    def remove_filler_words_advanced(self, text):
        """Pokročilé odstránenie výplňových slov"""
        fillers = [
            r'\behm+\b', r'\beh+\b', r'\bäh+m?\b', r'\bhmm+\b', r'\bhm+\b',
            r'\bno\s+tak\b', r'\btak\s+no\b', r'\bproste\s+tak\b',
            r'\bvlastne\s+tak\b', r'\bteda\s+tak\b', r'\bčiže\s+tak\b',
            r'\b(no|tak|teda|vlastne|čiže|proste)(\s+\1)+\b',
            r'\b(ehm|eh|äh|hmm|hm)\s*[,.]?\s*', 
            r'\s+(no|tak)\s+(?=\w)', r'\s+(teda|vlastne)\s+(?=\w)',
        ]
        
        for filler in fillers:
            text = re.sub(filler, ' ', text, flags=re.IGNORECASE)
        
        # Vyčistenie viacnásobných medzier
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()

    def add_proper_paragraphs(self, text):
        """Pridá odseky podľa slovenských pravidiel"""
        # Rozdelenie na vety
        sentences = re.split(r'([.!?]+)', text)
        
        if len(sentences) < 6:
            return text
        
        paragraphs = []
        current_paragraph = []
        sentence_count = 0
        
        for i in range(0, len(sentences)-1, 2):
            sentence = sentences[i].strip()
            punctuation = sentences[i+1] if i+1 < len(sentences) else '.'
            
            if sentence:
                current_paragraph.append(sentence + punctuation)
                sentence_count += 1
                
                # Nový odsek každých 3-5 viet alebo pri signálnych slovách
                should_break = (
                    sentence_count >= 3 and (
                        sentence_count >= 5 or
                        any(signal in sentence.lower() for signal in [
                            'potom', 'ďalej', 'nakoniec', 'takže', 'ale', 'však', 
                            'preto', 'prečo', 'vtedy', 'neskôr', 'medzitým',
                            'napriek tomu', 'okrem toho', 'navyše', 'teda'
                        ])
                    )
                )
                
                if should_break:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
                    sentence_count = 0
        
        # Pridanie posledného odseku
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        return '\n\n'.join(paragraphs)

    def correct_text_thoroughly(self, text):
        """Komplexná oprava textu"""
        original_length = len(text)
        
        # 1. Odstránenie výplňových slov
        text = self.remove_filler_words_advanced(text)
        
        # 2. Oprava diakritiky
        text = self.fix_diacritics(text)
        
        # 3. Oprava interpunkcie
        text = self.fix_punctuation_advanced(text)
        
        # 4. Oprava veľkých písmen
        text = self.fix_capitalization_slovak(text)
        
        # 5. Pridanie odsekov
        text = self.add_proper_paragraphs(text)
        
        # 6. Finálne čistenie
        text = text.strip()
        
        # 7. Kontrola a oprava častých chýb
        for error, correction in self.grammar_fixes.items():
            text = text.replace(error, correction)
        
        corrected_length = len(text)
        changes = abs(original_length - corrected_length)
        
        return text, changes

def process_file_thoroughly(file_path, corrector):
    """Dôkladne spracuje jeden súbor"""
    try:
        print(f"📖 Načítavam: {os.path.basename(file_path)}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extrakcia prepisu z obsahu
        lines = content.split('\n')
        transcript_start = 0
        header_lines = []
        
        # Nájdenie začiatku prepisu
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'text:', 'obsah:', 'transkript']):
                transcript_start = i + 1
                header_lines = lines[:i+1]
                break
        
        if transcript_start == 0:
            # Ak nenájdeme hlavičku, hľadáme samotný text
            for i, line in enumerate(lines):
                if len(line.strip()) > 50:  # Dlhší riadok = pravdepodobne začiatok textu
                    transcript_start = i
                    break
        
        transcript_lines = lines[transcript_start:]
        transcript_text = '\n'.join(transcript_lines).strip()
        
        if len(transcript_text) < 20:
            return False, "Príliš krátky text na spracovanie"
        
        print(f"🔧 Opravujem text ({len(transcript_text)} znakov)...")
        
        # Dôkladná oprava
        corrected_text, changes = corrector.correct_text_thoroughly(transcript_text)
        
        # Vytvorenie nového obsahu
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        
        new_content = [
            f"PREPIS EPIZÓDY: {file_name}",
            "=" * 70,
            f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "Dôkladne opravené - slovenčina, gramatika, pravopis",
            "",
            "PREPIS:",
            "-" * 50,
            "",
            corrected_text,
            "",
            "",
            "=" * 70,
            "INFORMÁCIE O OPRAVE:",
            f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"- Zmeny v texte: {changes} znakov",
            f"- Pôvodná dĺžka: {len(transcript_text)} znakov",
            f"- Opravená dĺžka: {len(corrected_text)} znakov",
            "- Opravené: diakritika, gramatika, interpunkcia, štruktúra",
            "- Jazyk: slovenčina (SK)",
            "- Kvalita: profesionálna úroveň"
        ]
        
        # Uloženie opraveného súboru
        corrected_file_path = file_path.replace('.txt', '_SK_corrected.txt')
        with open(corrected_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_content))
        
        print(f"✅ Uložené: {os.path.basename(corrected_file_path)}")
        
        return True, f"Úspešne opravené, {changes} zmien"
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False, f"Chyba: {e}"

def main():
    """Hlavná funkcia - spracuje prvých 5 súborov"""
    text_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("🇸🇰 === POKROČILÁ OPRAVA SLOVENSKÝCH TEXTOV ===")
    print(f"📁 Priečinok: {text_dir}")
    print("🎯 Spracovávam prvých 5 súborov s maximálnou presnosťou")
    print()
    
    if not os.path.exists(text_dir):
        print(f"❌ Priečinok {text_dir} neexistuje!")
        return
    
    # Nájdenie TXT súborov (bez už opravených)
    txt_files = []
    for file in os.listdir(text_dir):
        if (file.lower().endswith('.txt') and 
            not file.endswith('_cleaned.txt') and 
            not file.endswith('_SK_corrected.txt')):
            txt_files.append(os.path.join(text_dir, file))
    
    if not txt_files:
        print("❌ Nenašli sa žiadne súbory na spracovanie!")
        return
    
    # Spracovanie prvých 5 súborov
    files_to_process = txt_files[:5]
    print(f"📋 Spracovávam {len(files_to_process)} súborov:")
    for i, file_path in enumerate(files_to_process, 1):
        print(f"   {i}. {os.path.basename(file_path)}")
    print()
    
    # Vytvorenie korektora
    corrector = SlovakTextCorrector()
    
    # Spracovanie súborov
    successful = 0
    failed = 0
    total_changes = 0
    
    for i, file_path in enumerate(files_to_process, 1):
        print(f"[{i}/5] " + "="*50)
        
        success, message = process_file_thoroughly(file_path, corrector)
        
        if success:
            successful += 1
            # Extrakcia počtu zmien
            if "zmien" in message:
                try:
                    changes = int(message.split()[2])
                    total_changes += changes
                except:
                    pass
        else:
            failed += 1
        
        print(f"📊 Výsledok: {message}")
        print()
        time.sleep(0.5)
    
    print("🏁 === FINÁLNY VÝSLEDOK ===")
    print(f"✅ Úspešne opravené: {successful}/5")
    print(f"❌ Neúspešné: {failed}/5")
    print(f"📈 Celkom zmien: {total_changes}")
    print(f"📁 Opravené súbory: *_SK_corrected.txt")
    print(f"💾 Umiestnenie: {text_dir}")
    print()
    print("🎉 Prvých 5 súborov je teraz v perfektnej slovenčine!")

if __name__ == "__main__":
    main()
