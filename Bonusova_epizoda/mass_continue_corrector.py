#!/usr/bin/env python3
"""
Hromadná oprava všetkých zostávajúcich epizód
"""

import os
import time
import requests
import json

class MassContinueCorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_remaining_episodes(self):
        """Získa všetky zostávajúce epizódy na opravu"""
        if not os.path.exists(self.prepisy_dir):
            return []
        
        # Nájdenie všetkých _cleaned.txt súborov (najkvalitnejšie)
        cleaned_files = []
        for file in os.listdir(self.prepisy_dir):
            if file.endswith('_cleaned.txt'):
                cleaned_files.append(file)
        
        # Nájdenie už opravených súborov
        corrected_episodes = set()
        if os.path.exists(self.corrected_dir):
            for file in os.listdir(self.corrected_dir):
                if file.endswith('_OpenAI_corrected.txt'):
                    base_name = file.replace('_OpenAI_corrected.txt', '')
                    corrected_episodes.add(f"{base_name}_cleaned.txt")
        
        # Zostávajúce súbory na opravu
        remaining_episodes = []
        for file in sorted(cleaned_files):
            if file not in corrected_episodes:
                base_name = file.replace('_cleaned.txt', '')
                remaining_episodes.append({
                    'base_name': base_name,
                    'file_name': file,
                    'file_path': os.path.join(self.prepisy_dir, file)
                })
        
        return remaining_episodes
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai(self, text, episode_name):
        """Opraví text pomocou OpenAI API"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 8000:
                text = text[:8000] + "\n\n[Text skrátený kvôli limitom API]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský". Zachovaj atmosféru a štýl.

ÚLOHY:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - použij slovenské úvodzovky „" a pomlčky –
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Neodstraňuj dôležité detaily ani dialógy
8. Vráť len opravený text bez komentárov

EPIZÓDA: {episode_name}

TEXT NA OPRAVU:
{text}"""
            
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk a korektúru textov. Opravuješ texty z horor podcastu s maximálnou presnosťou. Zachovávaš atmosféru a štýl príbehov."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.2
            }
            
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                corrected_text = result['choices'][0]['message']['content'].strip()
                return corrected_text
            else:
                print(f"❌ API chyba: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, episode_info):
        """Uloží opravený text"""
        try:
            base_name = episode_info['base_name']
            output_file = os.path.join(self.corrected_dir, f"{base_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {base_name}",
                "=" * 70,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "Podcast: Krvavý Dobšinský",
                "",
                corrected_text,
                "",
                "",
                "=" * 70,
                "INFORMÁCIE O OPRAVE:",
                f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini API",
                f"- Pôvodný súbor: {episode_info['file_name']}",
                f"- Dĺžka opraveného textu: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika, interpunkcia, štruktúra",
                "- Odstránené: výplňové slová, chyby z rozpoznávania reči",
                "- Pridané: logické odseky, správna interpunkcia",
                "- Jazyk: slovenčina (SK) - profesionálna úroveň",
                "- Zachovaný štýl: horor podcast atmosféra"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_all_remaining(self):
        """Spracuje všetky zostávajúce epizódy"""
        print("🚀 === HROMADNÁ OPRAVA VŠETKÝCH ZOSTÁVAJÚCICH EPIZÓD ===")
        print(f"🕐 Začiatok: {time.strftime('%H:%M:%S')}")
        print()
        
        # Aktuálny stav
        try:
            current_corrected = len([f for f in os.listdir(self.corrected_dir) 
                                   if f.endswith('_OpenAI_corrected.txt')])
            print(f"📊 Už opravených: {current_corrected} epizód")
        except:
            current_corrected = 0
            print(f"📊 Už opravených: 0 epizód")
        
        # Získanie zostávajúcich epizód
        remaining_episodes = self.get_remaining_episodes()
        
        if not remaining_episodes:
            print("🎉 Všetky epizódy už sú opravené!")
            return
        
        print(f"🎯 Zostáva na opravu: {len(remaining_episodes)} epizód")
        print()
        
        # Zobrazenie prvých 15 epizód
        print("📋 Epizódy na spracovanie:")
        for i, ep in enumerate(remaining_episodes[:15], 1):
            print(f"   {i:2d}. {ep['base_name']}")
        
        if len(remaining_episodes) > 15:
            print(f"   ... a ďalších {len(remaining_episodes)-15} epizód")
        
        print()
        print("🚀 Začínam hromadnú opravu...")
        print()
        
        # Spracovanie všetkých epizód
        successful = 0
        failed = 0
        start_time = time.time()
        
        for i, episode in enumerate(remaining_episodes, 1):
            print(f"[{i:2d}/{len(remaining_episodes)}] {episode['base_name']}")
            
            # Extrakcia textu
            story_text = self.extract_story_text(episode['file_path'])
            if not story_text or len(story_text) < 50:
                print(f"   ❌ Neplatný text ({len(story_text) if story_text else 0} znakov)")
                failed += 1
                continue
            
            print(f"   📖 Text: {len(story_text)} znakov")
            
            # Oprava cez OpenAI
            print(f"   🤖 OpenAI...")
            corrected_text = self.correct_text_with_openai(story_text, episode['base_name'])
            if not corrected_text:
                print(f"   ❌ Oprava zlyhala")
                failed += 1
                continue
            
            print(f"   ✅ Opravené: {len(corrected_text)} znakov")
            
            # Uloženie
            output_file = self.save_corrected_text(corrected_text, episode)
            if output_file:
                print(f"   💾 Uložené: {os.path.basename(output_file)}")
                successful += 1
            else:
                print(f"   ❌ Uloženie zlyhalo")
                failed += 1
            
            # Štatistiky pokroku
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining_time = avg_time * (len(remaining_episodes) - i)
            
            print(f"   ⏱️  Čas: {elapsed/60:.1f}min | Zostáva: ~{remaining_time/60:.1f}min")
            
            # Pauza medzi požiadavkami
            if i < len(remaining_episodes):
                print("   ⏳ Pauza 2s...")
                time.sleep(2)
            
            print()
            
            # Priebežný súhrn každých 10 epizód
            if i % 10 == 0:
                total_corrected = current_corrected + successful
                print(f"📊 === PRIEBEŽNÝ SÚHRN ===")
                print(f"✅ Úspešne v tejto dávke: {successful}")
                print(f"❌ Neúspešne v tejto dávke: {failed}")
                print(f"📈 Celkom opravených: {total_corrected} epizód")
                print(f"⏱️  Priemerný čas: {avg_time:.1f}s na epizódu")
                print()
        
        # Finálny súhrn
        total_time = time.time() - start_time
        final_corrected = current_corrected + successful
        
        print(f"🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne opravené: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📊 Celkom opravených epizód: {final_corrected}")
        print(f"⏱️  Celkový čas: {total_time/60:.1f} minút")
        print(f"📁 Všetky súbory: {self.corrected_dir}")
        print()
        
        if successful > 0:
            print(f"🎉 Úspešne opravených {successful} nových epizód!")
            print(f"📚 Všetky texty sú teraz v profesionálnej slovenčine")
            print(f"🏆 Kvalita: OpenAI GPT-4o-mini")
        
        if failed > 0:
            print(f"⚠️  {failed} epizód sa nepodarilo opraviť")
            print(f"💡 Môžete ich skúsiť opraviť manuálne")

def main():
    """Hlavná funkcia"""
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = MassContinueCorrector(api_key)
    corrector.process_all_remaining()

if __name__ == "__main__":
    main()
