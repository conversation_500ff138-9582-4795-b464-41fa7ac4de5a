#!/usr/bin/env python3
"""
Jednoduchá príprava textu a uloženie do corrected priečinka
"""

import os
import time

def get_text_file():
    """Vyberie textový súbor"""
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    if not os.path.exists(prepisy_dir):
        return None
    
    # Nájdenie prvého _cleaned.txt súboru
    for file in sorted(os.listdir(prepisy_dir)):
        if file.endswith('_cleaned.txt'):
            return os.path.join(prepisy_dir, file)
    
    return None

def extract_story_text(file_path):
    """Extrahuje príbeh z súboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        story_start = 0
        story_end = len(lines)
        
        # Nájdenie začiatku príbehu
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'text:', '---']):
                story_start = i + 1
                break
        
        # Nájdenie konca príbehu
        for i in range(len(lines)-1, -1, -1):
            if any(keyword in line.lower() for keyword in 
                   ['informácie', 'spracované:', 'koniec', '===']):
                story_end = i
                break
        
        story_lines = lines[story_start:story_end]
        story_text = '\n'.join(story_lines).strip()
        
        return story_text
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return None

def save_text_for_correction(story_text, file_name):
    """Uloží text pripravený na opravu"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    os.makedirs(corrected_dir, exist_ok=True)
    
    # Vytvorenie súboru na opravu
    prep_file = os.path.join(corrected_dir, f"{file_name}_for_ChatGPT.txt")
    
    with open(prep_file, 'w', encoding='utf-8') as f:
        f.write(f"PRÍBEH NA OPRAVU: {file_name}\n")
        f.write("=" * 50 + "\n\n")
        f.write("INŠTRUKCIE PRE CHATGPT:\n")
        f.write("Oprav tento slovenský text - gramatiku, pravopis, diakritiku, interpunkciu.\n")
        f.write("Rozdeľ na odseky. Odstráň výplňové slová. Zachovaj obsah a štýl.\n\n")
        f.write("TEXT NA OPRAVU:\n")
        f.write("-" * 30 + "\n\n")
        f.write(story_text)
        f.write("\n\n" + "-" * 30 + "\n")
        f.write("KONIEC TEXTU NA OPRAVU")
    
    return prep_file

def create_corrected_version(story_text, file_name):
    """Vytvorí základnú opravu textu"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    os.makedirs(corrected_dir, exist_ok=True)
    
    # Základné opravy
    corrected_text = story_text
    
    # Oprava častých chýb
    corrections = {
        'co': 'čo', 'ci': 'či', 'ze': 'že', 'ked': 'keď',
        'uz': 'už', 'este': 'ešte', 'dalej': 'ďalej',
        'ktory': 'ktorý', 'ktora': 'ktorá', 'ktore': 'ktoré',
        'preco': 'prečo', 'takze': 'takže', 'ano': 'áno',
        'ma': 'má', 'mam': 'mám', 'su': 'sú',
        'viac': 'viac', 'vsak': 'však', 'vsetko': 'všetko',
        'nas': 'náš', 'nasa': 'naša', 'nase': 'naše',
        'vas': 'váš', 'vasa': 'vaša', 'vase': 'vaše'
    }
    
    for old, new in corrections.items():
        corrected_text = corrected_text.replace(f' {old} ', f' {new} ')
        corrected_text = corrected_text.replace(f' {old.capitalize()} ', f' {new.capitalize()} ')
    
    # Uloženie opraveného súboru
    output_file = os.path.join(corrected_dir, f"{file_name}_corrected.txt")
    
    content = [
        f"EPIZÓDA: {file_name}",
        "=" * 60,
        f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
        "Základná oprava slovenčiny",
        "",
        corrected_text,
        "",
        "",
        "=" * 60,
        "INFORMÁCIE:",
        f"- Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}",
        "- Opravené: základná slovenčina",
        "- Pre pokročilú opravu použite ChatGPT"
    ]
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(content))
    
    return output_file

def main():
    """Hlavná funkcia"""
    print("📝 === PRÍPRAVA TEXTU PRE OPRAVU ===")
    print()
    
    # Výber súboru
    file_path = get_text_file()
    if not file_path:
        print("❌ Nenašiel sa žiadny súbor!")
        return
    
    file_name = os.path.basename(file_path).replace('_cleaned.txt', '')
    print(f"📄 Vybraný súbor: {file_name}")
    
    # Extrakcia textu
    story_text = extract_story_text(file_path)
    if not story_text:
        print("❌ Nepodarilo sa extrahovať text!")
        return
    
    print(f"📖 Text extrahovaný ({len(story_text)} znakov)")
    
    # Uloženie pre ChatGPT
    prep_file = save_text_for_correction(story_text, file_name)
    print(f"✅ Pripravené pre ChatGPT: {os.path.basename(prep_file)}")
    
    # Základná oprava
    corrected_file = create_corrected_version(story_text, file_name)
    print(f"✅ Základná oprava: {os.path.basename(corrected_file)}")
    
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    print(f"\n📁 Súbory uložené v: {corrected_dir}")
    
    # Zobrazenie obsahu priečinka
    print(f"\n📋 Obsah priečinka corrected:")
    try:
        files = os.listdir(corrected_dir)
        for i, file in enumerate(files, 1):
            print(f"   {i}. {file}")
    except:
        print("   Prázdny priečinok")
    
    print(f"\n🎉 Hotovo! Súbory sú pripravené.")

if __name__ == "__main__":
    main()
