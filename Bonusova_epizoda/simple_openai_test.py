#!/usr/bin/env python3
"""
Jednoduchý test OpenAI API s kratším textom
"""

import os
import time
import requests
import json

def test_openai_api():
    """Test OpenAI API s jednoduchým textom"""
    
    api_key = "********************************************************************************************************************************************************************"
    
    url = "https://api.openai.com/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Testovací slovenský text s chybami
    test_text = """
    Toto je testovaci text v slovencine. Ma vela chyb v gramatike a pravopise.
    Chcem aby bol opraveny a mal spravnu diakritiku. Takze prosim oprav ho.
    Uz som unaveny z toho ze musim stale opravovat texty rucne.
    """
    
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {
                "role": "system",
                "content": "Si expert na slovenský jazyk. Opravuješ texty s maximálnou presnosťou."
            },
            {
                "role": "user", 
                "content": f"Oprav tento slovenský text - gramatiku, pravopis, diakritiku:\n\n{test_text}"
            }
        ],
        "max_tokens": 500,
        "temperature": 0.2
    }
    
    print("🧪 === TEST OPENAI API ===")
    print(f"📝 Testovací text:")
    print(test_text)
    print()
    print("📡 Posielam do OpenAI...")
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            corrected_text = result['choices'][0]['message']['content'].strip()
            
            print("✅ Úspešná odpoveď!")
            print(f"🔧 Opravený text:")
            print(corrected_text)
            
            # Uloženie do súboru
            corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
            os.makedirs(corrected_dir, exist_ok=True)
            
            test_file = os.path.join(corrected_dir, "OpenAI_test_result.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("OPENAI API TEST\n")
                f.write("=" * 50 + "\n\n")
                f.write("PÔVODNÝ TEXT:\n")
                f.write(test_text)
                f.write("\n\nOPRAVENÝ TEXT:\n")
                f.write(corrected_text)
                f.write(f"\n\nDátum: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print(f"\n💾 Test uložený: {test_file}")
            return True
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"Odpoveď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def correct_short_story():
    """Opraví krátky príbeh z prepisu"""
    
    # Nájdenie najkratšieho súboru
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    if not os.path.exists(prepisy_dir):
        print("❌ Priečinok Prepisy neexistuje!")
        return False
    
    # Nájdenie najmenšieho súboru
    smallest_file = None
    smallest_size = float('inf')
    
    for file in os.listdir(prepisy_dir):
        if file.endswith('_cleaned.txt'):
            file_path = os.path.join(prepisy_dir, file)
            size = os.path.getsize(file_path)
            if size < smallest_size:
                smallest_size = size
                smallest_file = file_path
    
    if not smallest_file:
        print("❌ Nenašiel sa žiadny súbor!")
        return False
    
    print(f"📄 Najmenší súbor: {os.path.basename(smallest_file)} ({smallest_size} bytov)")
    
    # Načítanie a skrátenie textu
    try:
        with open(smallest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extrakcia príbehu
        lines = content.split('\n')
        story_lines = []
        
        for line in lines:
            if len(line.strip()) > 10 and not any(keyword in line.lower() for keyword in 
                   ['informácie', 'spracované:', 'koniec', '===', 'prepis:']):
                story_lines.append(line.strip())
        
        story_text = ' '.join(story_lines)
        
        # Skrátenie na prvých 2000 znakov
        if len(story_text) > 2000:
            story_text = story_text[:2000] + "..."
        
        print(f"📖 Text na opravu ({len(story_text)} znakov):")
        print(story_text[:200] + "...")
        
        # OpenAI oprava
        api_key = "********************************************************************************************************************************************************************"
        
        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "system",
                    "content": "Si expert na slovenský jazyk. Opravuješ texty z horor podcastu."
                },
                {
                    "role": "user", 
                    "content": f"Oprav tento slovenský text z horor podcastu - gramatiku, pravopis, diakritiku, interpunkciu. Rozdeľ na odseky. Zachovaj štýl:\n\n{story_text}"
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.2
        }
        
        print("\n📡 Posielam do OpenAI...")
        response = requests.post(url, headers=headers, json=payload, timeout=45)
        
        if response.status_code == 200:
            result = response.json()
            corrected_text = result['choices'][0]['message']['content'].strip()
            
            # Uloženie
            corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
            os.makedirs(corrected_dir, exist_ok=True)
            
            episode_name = os.path.basename(smallest_file).replace('_cleaned.txt', '')
            output_file = os.path.join(corrected_dir, f"{episode_name}_OpenAI_short.txt")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"EPIZÓDA: {episode_name} (skrátená verzia)\n")
                f.write("=" * 60 + "\n\n")
                f.write(corrected_text)
                f.write(f"\n\n--- Opravené OpenAI ---\n")
                f.write(f"Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print(f"✅ Úspešne opravené!")
            print(f"💾 Uložené: {output_file}")
            print(f"\n📖 Ukážka opraveného textu:")
            print("-" * 50)
            print(corrected_text[:300] + "...")
            print("-" * 50)
            
            return True
        else:
            print(f"❌ API chyba: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def main():
    """Hlavná funkcia"""
    print("🤖 === OPENAI API TEST A DEMO ===")
    print()
    
    # Test API
    print("1️⃣ Testovanie API...")
    if test_openai_api():
        print("✅ API test úspešný!")
    else:
        print("❌ API test neúspešný!")
        return
    
    print("\n" + "="*60 + "\n")
    
    # Oprava skutočného príbehu
    print("2️⃣ Oprava skutočného príbehu...")
    if correct_short_story():
        print("✅ Príbeh úspešne opravený!")
    else:
        print("❌ Oprava príbehu neúspešná!")
    
    print(f"\n🎉 Demo dokončené!")

if __name__ == "__main__":
    main()
