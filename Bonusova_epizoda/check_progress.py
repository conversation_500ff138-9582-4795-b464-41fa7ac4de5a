#!/usr/bin/env python3
"""
Kontrola pokroku hromadnej opravy
"""

import os
import time

def check_progress():
    """Skontroluje pokrok opravy"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("📊 === KONTROLA POKROKU OPRAVY ===")
    print(f"🕐 Čas: {time.strftime('%H:%M:%S')}")
    print()
    
    # Počet súborov v corrected
    try:
        corrected_files = [f for f in os.listdir(corrected_dir) if f.endswith('.txt')]
        openai_files = [f for f in corrected_files if 'OpenAI' in f]
        
        print(f"📁 Priečinok corrected:")
        print(f"   Celkom TXT súborov: {len(corrected_files)}")
        print(f"   OpenAI opravené: {len(openai_files)}")
        
        # Posledné súbory
        if openai_files:
            openai_files.sort(key=lambda x: os.path.getmtime(os.path.join(corrected_dir, x)))
            print(f"\n📋 Posledných 5 OpenAI súborov:")
            for i, file in enumerate(openai_files[-5:], 1):
                mtime = os.path.getmtime(os.path.join(corrected_dir, file))
                time_str = time.strftime('%H:%M:%S', time.localtime(mtime))
                print(f"   {i}. {file} ({time_str})")
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní corrected: {e}")
    
    # Počet súborov v prepisy
    try:
        prepisy_files = [f for f in os.listdir(prepisy_dir) if f.endswith('.txt')]
        print(f"\n📁 Priečinok Prepisy:")
        print(f"   Celkom TXT súborov: {len(prepisy_files)}")
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní Prepisy: {e}")
    
    # Odhad pokroku
    if 'openai_files' in locals() and 'prepisy_files' in locals():
        progress = (len(openai_files) / len(prepisy_files)) * 100
        print(f"\n📈 Odhadovaný pokrok: {progress:.1f}%")
        print(f"   Opravené: {len(openai_files)}")
        print(f"   Zostáva: {len(prepisy_files) - len(openai_files)}")

if __name__ == "__main__":
    check_progress()
