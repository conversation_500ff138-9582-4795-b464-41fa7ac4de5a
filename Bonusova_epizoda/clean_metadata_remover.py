#!/usr/bin/env python3
"""
Odstráni všetky metadata a technické informácie zo súborov
Nechá len čistý text prí<PERSON>hov
"""

import os
import re
import time

class MetadataRemover:
    def __init__(self):
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        self.web_ready_dir = "/Users/<USER>/Desktop/Krvavý Audio/web_ready"
        self.clean_dir = "/Users/<USER>/Desktop/Krvavý Audio/clean_stories"
        os.makedirs(self.clean_dir, exist_ok=True)
    
    def extract_pure_story(self, file_path):
        """Extrahuje len čistý príbeh bez akýchkoľvek metadát"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_lines = []
            
            # Preskočenie všetkých hlavičiek a metadát
            in_story = False
            
            for line in lines:
                line_stripped = line.strip()
                
                # Preskočenie prázdnych riadkov na začiatku
                if not in_story and not line_stripped:
                    continue
                
                # Preskočenie hlavičiek a metadát
                if any(skip in line_stripped for skip in [
                    'EPIZÓDA:', 'Opravené:', 'Opravené pomocou:', 'Kvalita:', 
                    'Podcast:', 'Dátum:', 'Nástroj:', 'Pôvodný súbor:', 
                    'Dĺžka:', 'Jazyk:', 'INFORMÁCIE:', 'Spracované:',
                    '=' * 10, '-' * 10, '*Epizóda podcastu*', '*Spracované:*',
                    '*Kvalita:*', '# '
                ]):
                    continue
                
                # Ak riadok obsahuje len špeciálne znaky, preskočiť
                if re.match(r'^[=\-*#\s]*$', line_stripped):
                    continue
                
                # Ak sme našli prvý riadok príbehu
                if line_stripped and not in_story:
                    in_story = True
                
                # Pridanie riadku príbehu
                if in_story:
                    # Zastavenie pri informáciách na konci
                    if any(stop in line_stripped.lower() for stop in [
                        'informácie o oprave:', 'informácie:', 'dátum opravy:',
                        'nástroj:', 'pôvodný súbor:', 'opravené:', 'jazyk:'
                    ]):
                        break
                    
                    story_lines.append(line)
            
            # Spojenie a vyčistenie
            story_text = '\n'.join(story_lines).strip()
            
            # Odstránenie zvyšných metadát na konci
            story_text = re.sub(r'\n\s*=+\s*\n.*$', '', story_text, flags=re.DOTALL)
            story_text = re.sub(r'\n\s*-+\s*\n.*$', '', story_text, flags=re.DOTALL)
            
            # Finálne čistenie
            story_text = re.sub(r'\n{3,}', '\n\n', story_text)  # Max 2 nové riadky
            story_text = story_text.strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def format_clean_story(self, text):
        """Naformátuje čistý príbeh"""
        if not text:
            return ""
        
        # Základné čistenie
        text = re.sub(r'\s+', ' ', text)  # Viacnásobné medzery
        text = re.sub(r'\n\s+', '\n', text)  # Medzery na začiatku riadkov
        text = re.sub(r'\s+\n', '\n', text)  # Medzery na konci riadkov
        
        # Rozdelenie na odseky pre lepšiu čitateľnosť
        sentences = re.split(r'([.!?]+)', text)
        paragraphs = []
        current_paragraph = []
        sentence_count = 0
        
        for i in range(0, len(sentences)-1, 2):
            sentence = sentences[i].strip()
            punctuation = sentences[i+1] if i+1 < len(sentences) else '.'
            
            if sentence:
                current_paragraph.append(sentence + punctuation)
                sentence_count += 1
                
                # Nový odsek po 3-5 vetách alebo pri signálnych slovách
                should_break = (
                    sentence_count >= 3 and (
                        sentence_count >= 5 or
                        any(signal in sentence.lower() for signal in [
                            'potom', 'ďalej', 'nakoniec', 'takže', 'ale', 'však', 
                            'preto', 'vtedy', 'neskôr', 'zrazu', 'náhle',
                            'o chvíľu', 'za chvíľu', 'po chvíli', 'keď som', 'keď sa'
                        ])
                    )
                )
                
                if should_break:
                    paragraphs.append(' '.join(current_paragraph))
                    current_paragraph = []
                    sentence_count = 0
        
        # Pridanie posledného odseku
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        # Spojenie odsekov
        formatted_text = '\n\n'.join(paragraphs)
        formatted_text = re.sub(r'\n{3,}', '\n\n', formatted_text)
        formatted_text = formatted_text.strip()
        
        return formatted_text
    
    def process_corrected_files(self):
        """Spracuje všetky opravené súbory"""
        print("🧹 === ODSTRÁNENIE METADÁT ZO SÚBOROV ===")
        print(f"📁 Zdrojový priečinok: {self.corrected_dir}")
        print(f"📁 Cieľový priečinok: {self.clean_dir}")
        print()
        
        if not os.path.exists(self.corrected_dir):
            print(f"❌ Priečinok {self.corrected_dir} neexistuje!")
            return
        
        # Nájdenie OpenAI opravených súborov
        corrected_files = []
        for file in os.listdir(self.corrected_dir):
            if file.endswith('_OpenAI_corrected.txt'):
                corrected_files.append(file)
        
        if not corrected_files:
            print("❌ Nenašli sa žiadne OpenAI opravené súbory!")
            return
        
        print(f"📊 Spracovávam {len(corrected_files)} súborov")
        print()
        
        successful = 0
        failed = 0
        
        for i, file_name in enumerate(sorted(corrected_files), 1):
            episode_name = file_name.replace('_OpenAI_corrected.txt', '')
            file_path = os.path.join(self.corrected_dir, file_name)
            
            print(f"[{i:2d}/{len(corrected_files)}] {episode_name}")
            
            # Extrakcia čistého príbehu
            clean_story = self.extract_pure_story(file_path)
            if not clean_story:
                print(f"   ❌ Nepodarilo sa extrahovať príbeh")
                failed += 1
                continue
            
            if len(clean_story) < 100:
                print(f"   ⚠️  Príbeh príliš krátky ({len(clean_story)} znakov)")
                failed += 1
                continue
            
            print(f"   📖 Čistý príbeh: {len(clean_story)} znakov")
            
            # Formátovanie
            formatted_story = self.format_clean_story(clean_story)
            print(f"   📝 Naformátovaný: {len(formatted_story)} znakov")
            
            # Uloženie čistého súboru
            clean_file_path = os.path.join(self.clean_dir, f"{episode_name}.txt")
            
            try:
                with open(clean_file_path, 'w', encoding='utf-8') as f:
                    f.write(formatted_story)
                
                print(f"   💾 Uložené: {episode_name}.txt")
                successful += 1
                
            except Exception as e:
                print(f"   ❌ Chyba pri ukladaní: {e}")
                failed += 1
            
            print()
        
        # Finálny súhrn
        print(f"🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne vyčistené: {successful}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Čisté príbehy: {self.clean_dir}")
        
        # Zobrazenie obsahu clean priečinka
        try:
            clean_files = [f for f in os.listdir(self.clean_dir) if f.endswith('.txt')]
            print(f"\n📋 Čisté súbory ({len(clean_files)}):")
            for i, file in enumerate(sorted(clean_files)[:15], 1):
                print(f"   {i:2d}. {file}")
            
            if len(clean_files) > 15:
                print(f"   ... a ďalších {len(clean_files)-15} súborov")
                
        except Exception as e:
            print(f"❌ Chyba pri zobrazení: {e}")
        
        print(f"\n🎉 Všetky súbory sú vyčistené od metadát!")
        print(f"📚 {successful} príbehov v čistom formáte")
        print(f"🌟 Len text príbehov, žiadne technické informácie")
    
    def process_web_ready_files(self):
        """Spracuje aj web-ready súbory ak existujú"""
        if not os.path.exists(self.web_ready_dir):
            return
        
        print(f"\n🌐 === ČISTENIE WEB-READY SÚBOROV ===")
        
        web_files = [f for f in os.listdir(self.web_ready_dir) if f.endswith('.txt')]
        
        if not web_files:
            print("📂 Žiadne web-ready súbory na čistenie")
            return
        
        print(f"📊 Čistím {len(web_files)} web-ready súborov")
        
        for file_name in web_files:
            file_path = os.path.join(self.web_ready_dir, file_name)
            
            # Extrakcia čistého textu
            clean_story = self.extract_pure_story(file_path)
            if clean_story and len(clean_story) > 100:
                formatted_story = self.format_clean_story(clean_story)
                
                # Prepísanie súboru len čistým textom
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(formatted_story)
        
        print(f"✅ Web-ready súbory vyčistené")

def main():
    """Hlavná funkcia"""
    remover = MetadataRemover()
    remover.process_corrected_files()
    remover.process_web_ready_files()

if __name__ == "__main__":
    main()
