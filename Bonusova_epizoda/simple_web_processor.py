#!/usr/bin/env python3
"""
Jednoduchý procesor pre web-ready texty
"""

import os
import re
import time

def process_corrected_files():
    """Spracuje opravené súbory pre web"""
    
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    web_ready_dir = "/Users/<USER>/Desktop/Krvavý Audio/web_ready"
    
    print("🌐 === PRÍPRAVA TEXTOV PRE WEB ===")
    print(f"📁 Zdrojový priečinok: {corrected_dir}")
    print(f"📁 Cieľový priečinok: {web_ready_dir}")
    print()
    
    # Vytvorenie cieľového priečinka
    os.makedirs(web_ready_dir, exist_ok=True)
    
    # Kontrola existencie zdrojového priečinka
    if not os.path.exists(corrected_dir):
        print(f"❌ Priečinok {corrected_dir} neexistuje!")
        return
    
    # Nájdenie OpenAI opravených súborov
    corrected_files = []
    for file in os.listdir(corrected_dir):
        if file.endswith('_OpenAI_corrected.txt'):
            corrected_files.append(file)
    
    if not corrected_files:
        print("❌ Nenašli sa žiadne OpenAI opravené súbory!")
        return
    
    print(f"📊 Nájdených {len(corrected_files)} súborov na spracovanie")
    print()
    
    successful = 0
    issues_found = 0
    
    for i, file_name in enumerate(sorted(corrected_files), 1):
        episode_name = file_name.replace('_OpenAI_corrected.txt', '')
        file_path = os.path.join(corrected_dir, file_name)
        
        print(f"[{i:2d}/{len(corrected_files)}] {episode_name}")
        
        try:
            # Načítanie súboru
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extrakcia čistého textu
            lines = content.split('\n')
            
            # Nájdenie začiatku textu (po hlavičke)
            text_start = 0
            for j, line in enumerate(lines):
                if line.strip() == "" and j > 5:
                    text_start = j + 1
                    break
            
            # Nájdenie konca textu (pred informáciami)
            text_end = len(lines)
            for j in range(len(lines)-1, -1, -1):
                if "=" in line and ("INFORMÁCIE" in content or "informácie" in content):
                    text_end = j - 2
                    break
            
            # Extrakcia čistého textu
            clean_lines = lines[text_start:text_end]
            clean_text = '\n'.join(clean_lines).strip()
            
            if len(clean_text) < 100:
                print(f"   ⚠️  Text príliš krátky ({len(clean_text)} znakov)")
                issues_found += 1
                continue
            
            print(f"   📖 Extrahovaný text: {len(clean_text)} znakov")
            
            # Základné čistenie textu
            # Odstránenie viacnásobných medzier
            clean_text = re.sub(r'\s+', ' ', clean_text)
            clean_text = re.sub(r'\n\s+', '\n', clean_text)
            clean_text = re.sub(r'\s+\n', '\n', clean_text)
            
            # Rozdelenie na odseky
            sentences = re.split(r'([.!?]+)', clean_text)
            paragraphs = []
            current_paragraph = []
            sentence_count = 0
            
            for k in range(0, len(sentences)-1, 2):
                sentence = sentences[k].strip()
                punctuation = sentences[k+1] if k+1 < len(sentences) else '.'
                
                if sentence:
                    current_paragraph.append(sentence + punctuation)
                    sentence_count += 1
                    
                    # Nový odsek po 3-5 vetách
                    if sentence_count >= 3 and (sentence_count >= 5 or 
                        any(signal in sentence.lower() for signal in [
                            'potom', 'ďalej', 'takže', 'ale', 'však', 'preto', 'vtedy'
                        ])):
                        paragraphs.append(' '.join(current_paragraph))
                        current_paragraph = []
                        sentence_count = 0
            
            # Pridanie posledného odseku
            if current_paragraph:
                paragraphs.append(' '.join(current_paragraph))
            
            # Spojenie odsekov
            formatted_text = '\n\n'.join(paragraphs)
            formatted_text = re.sub(r'\n{3,}', '\n\n', formatted_text)
            formatted_text = formatted_text.strip()
            
            print(f"   📝 Naformátovaný text: {len(formatted_text)} znakov")
            
            # Vytvorenie web-ready súboru
            web_file_path = os.path.join(web_ready_dir, f"{episode_name}.txt")
            
            web_content = [
                f"# {episode_name}",
                "",
                "*Epizóda podcastu Krvavý Dobšinský*",
                "",
                "---",
                "",
                formatted_text,
                "",
                "---",
                "",
                f"*Spracované: {time.strftime('%Y-%m-%d')}*",
                "*Kvalita: Profesionálna slovenčina*"
            ]
            
            with open(web_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(web_content))
            
            print(f"   💾 Web súbor: {episode_name}.txt")
            successful += 1
            
        except Exception as e:
            print(f"   ❌ Chyba: {e}")
            issues_found += 1
        
        print()
    
    # Finálny súhrn
    print(f"🏁 === FINÁLNY VÝSLEDOK ===")
    print(f"✅ Úspešne spracované: {successful}")
    print(f"❌ Problémy: {issues_found}")
    print(f"📁 Web-ready súbory: {web_ready_dir}")
    
    # Zobrazenie obsahu web_ready priečinka
    try:
        web_files = [f for f in os.listdir(web_ready_dir) if f.endswith('.txt')]
        print(f"\n📋 Web-ready súbory ({len(web_files)}):")
        for i, file in enumerate(sorted(web_files)[:15], 1):
            print(f"   {i:2d}. {file}")
        
        if len(web_files) > 15:
            print(f"   ... a ďalších {len(web_files)-15} súborov")
            
    except Exception as e:
        print(f"❌ Chyba pri zobrazení: {e}")
    
    print(f"\n🎉 Texty sú pripravené na web publikovanie!")
    print(f"📚 {successful} epizód v čistom formáte")
    print(f"🌐 Pripravené pre CMS, blog alebo webstránku")

if __name__ == "__main__":
    process_corrected_files()
