#!/usr/bin/env python3
"""
Hromadný korektor všetkých epizód cez OpenAI API
Zabezpečuje proti duplikáciám a organizuje súbory
"""

import os
import time
import requests
import json
import re
from pathlib import Path

class MassOpenAICorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_unique_episodes(self):
        """Získa zoznam unikátnych epizód na spracovanie"""
        if not os.path.exists(self.prepisy_dir):
            return []
        
        # Nájdenie všetkých TXT súborov
        all_files = [f for f in os.listdir(self.prepisy_dir) if f.endswith('.txt')]
        
        # Vytvorenie mapy epizód
        episodes = {}
        
        for file in all_files:
            # Určenie základného názvu epizódy
            base_name = file
            
            # Odstránenie rôznych prípon
            suffixes_to_remove = [
                '_cleaned.txt', '_corrected.txt', '_info.txt', 
                '_prepis.txt', '_kompletny_prepis.txt', '_OpenAI_corrected.txt',
                '_ChatGPT_corrected.txt', '_FINAL_corrected.txt', '_for_ChatGPT.txt',
                '_OpenAI_short.txt', '.txt'
            ]
            
            for suffix in suffixes_to_remove:
                if base_name.endswith(suffix):
                    base_name = base_name[:-len(suffix)]
                    break
            
            # Preskočenie pomocných súborov
            if any(skip in file.lower() for skip in [
                'instructions', 'test', 'backup', 'temp'
            ]):
                continue
            
            # Priorita typov súborov (najlepší najprv)
            priority = 0
            if '_cleaned.txt' in file:
                priority = 3  # Najlepšie - vyčistené
            elif '.txt' in file and not any(x in file for x in ['_corrected', '_info', '_prepis']):
                priority = 2  # Pôvodné súbory
            elif '_prepis.txt' in file:
                priority = 1  # Základné prepisy
            
            # Uloženie len ak je lepšia verzia alebo prvá
            if base_name not in episodes or episodes[base_name]['priority'] < priority:
                episodes[base_name] = {
                    'file_name': file,
                    'file_path': os.path.join(self.prepisy_dir, file),
                    'priority': priority,
                    'base_name': base_name
                }
        
        # Vrátenie zoznamu najlepších verzií
        unique_episodes = list(episodes.values())
        unique_episodes.sort(key=lambda x: x['base_name'])
        
        return unique_episodes
    
    def check_already_corrected(self, base_name):
        """Kontroluje či epizóda už bola opravená"""
        possible_names = [
            f"{base_name}_OpenAI_corrected.txt",
            f"{base_name}_FINAL_corrected.txt",
            f"{base_name}_corrected.txt"
        ]
        
        for name in possible_names:
            if os.path.exists(os.path.join(self.corrected_dir, name)):
                return True, name
        
        return False, None
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===', 'dátum:']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            # Vyčistenie od prázdnych riadkov na začiatku a konci
            story_text = re.sub(r'^\s*\n+', '', story_text)
            story_text = re.sub(r'\n+\s*$', '', story_text)
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai(self, text, episode_name):
        """Opraví text pomocou OpenAI API"""
        try:
            # Skrátenie textu ak je príliš dlhý
            max_length = 8000
            if len(text) > max_length:
                text = text[:max_length] + "\n\n[Text skrátený kvôli limitom API]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský". Zachovaj atmosféru a štýl.

ÚLOHY:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - použij slovenské úvodzovky „" a pomlčky –
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Neodstraňuj dôležité detaily ani dialógy
8. Vráť len opravený text bez komentárov alebo vysvetlení

EPIZÓDA: {episode_name}

TEXT NA OPRAVU:
{text}"""
            
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk a korektúru textov. Opravuješ texty z horor podcastu s maximálnou presnosťou. Zachovávaš atmosféru a štýl príbehov."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.2
            }
            
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                corrected_text = result['choices'][0]['message']['content'].strip()
                
                # Vyčistenie odpovede od možných komentárov
                if "OPRAVENÝ TEXT:" in corrected_text:
                    corrected_text = corrected_text.split("OPRAVENÝ TEXT:")[-1].strip()
                
                return corrected_text
            else:
                print(f"❌ API chyba: {response.status_code} - {response.text[:200]}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba pri volaní OpenAI: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, episode_info):
        """Uloží opravený text"""
        try:
            base_name = episode_info['base_name']
            output_file = os.path.join(self.corrected_dir, f"{base_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {base_name}",
                "=" * 70,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "Podcast: Krvavý Dobšinský",
                "",
                corrected_text,
                "",
                "",
                "=" * 70,
                "INFORMÁCIE O OPRAVE:",
                f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini API",
                f"- Pôvodný súbor: {episode_info['file_name']}",
                f"- Dĺžka opraveného textu: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika, interpunkcia, štruktúra",
                "- Odstránené: výplňové slová, chyby z rozpoznávania reči",
                "- Pridané: logické odseky, správna interpunkcia",
                "- Jazyk: slovenčina (SK) - profesionálna úroveň",
                "- Zachovaný štýl: horor podcast atmosféra"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_episode(self, episode_info):
        """Spracuje jednu epizódu"""
        base_name = episode_info['base_name']
        file_path = episode_info['file_path']
        
        print(f"\n📄 {base_name}")
        print(f"   📁 Súbor: {episode_info['file_name']}")
        
        # Kontrola či už bola opravená
        already_corrected, existing_file = self.check_already_corrected(base_name)
        if already_corrected:
            print(f"   ⏭️  Už opravená: {existing_file}")
            return "skipped"
        
        # Extrakcia textu
        story_text = self.extract_story_text(file_path)
        if not story_text:
            print(f"   ❌ Nepodarilo sa extrahovať text")
            return "failed"
        
        if len(story_text) < 50:
            print(f"   ⚠️  Text príliš krátky ({len(story_text)} znakov)")
            return "failed"
        
        print(f"   📖 Text: {len(story_text)} znakov")
        
        # Oprava cez OpenAI
        print(f"   🤖 Posielam do OpenAI...")
        corrected_text = self.correct_text_with_openai(story_text, base_name)
        if not corrected_text:
            print(f"   ❌ Oprava zlyhala")
            return "failed"
        
        print(f"   ✅ Opravené: {len(corrected_text)} znakov")
        
        # Uloženie
        output_file = self.save_corrected_text(corrected_text, episode_info)
        if not output_file:
            print(f"   ❌ Uloženie zlyhalo")
            return "failed"
        
        print(f"   💾 Uložené: {os.path.basename(output_file)}")
        return "success"
    
    def process_all_episodes(self):
        """Spracuje všetky epizódy"""
        print("🤖 === HROMADNÁ OPRAVA CEZ OPENAI API ===")
        print(f"📁 Zdrojový priečinok: {self.prepisy_dir}")
        print(f"📁 Cieľový priečinok: {self.corrected_dir}")
        print()
        
        # Získanie unikátnych epizód
        print("🔍 Analyzujem epizódy...")
        episodes = self.get_unique_episodes()
        
        if not episodes:
            print("❌ Nenašli sa žiadne epizódy na spracovanie!")
            return
        
        print(f"📊 Nájdených {len(episodes)} unikátnych epizód")
        print()
        
        # Zobrazenie prvých 10 epizód
        print("📋 Epizódy na spracovanie:")
        for i, episode in enumerate(episodes[:10], 1):
            print(f"   {i:2d}. {episode['base_name']} ({episode['file_name']})")
        
        if len(episodes) > 10:
            print(f"   ... a ďalších {len(episodes)-10} epizód")
        
        print()
        
        # Spracovanie
        successful = 0
        failed = 0
        skipped = 0
        
        for i, episode in enumerate(episodes, 1):
            print(f"[{i:2d}/{len(episodes)}] " + "="*60)
            
            result = self.process_episode(episode)
            
            if result == "success":
                successful += 1
            elif result == "skipped":
                skipped += 1
            else:
                failed += 1
            
            # Pauza medzi požiadavkami (rešpektovanie rate limitov)
            if i < len(episodes) and result == "success":
                print("   ⏳ Pauza 3 sekundy...")
                time.sleep(3)
        
        print(f"\n🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne opravené: {successful}")
        print(f"⏭️  Preskočené (už opravené): {skipped}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📊 Celkom spracovaných: {successful + skipped}")
        print(f"📁 Opravené súbory: {self.corrected_dir}")
        
        # Zobrazenie obsahu corrected priečinka
        try:
            corrected_files = [f for f in os.listdir(self.corrected_dir) 
                             if f.endswith('_OpenAI_corrected.txt')]
            print(f"\n📋 OpenAI opravené súbory ({len(corrected_files)}):")
            for i, file in enumerate(sorted(corrected_files)[:15], 1):
                print(f"   {i:2d}. {file}")
            
            if len(corrected_files) > 15:
                print(f"   ... a ďalších {len(corrected_files)-15} súborov")
                
        except Exception as e:
            print(f"❌ Chyba pri zobrazení výsledkov: {e}")

def main():
    """Hlavná funkcia"""
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = MassOpenAICorrector(api_key)
    corrector.process_all_episodes()

if __name__ == "__main__":
    main()
