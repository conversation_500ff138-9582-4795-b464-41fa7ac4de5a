#!/usr/bin/env python3
"""
Automatický OpenAI korektor - spracuje prvých 3 súborov
"""

import os
import time
import requests
import json

class AutoOpenAICorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_text_files(self, max_files=3):
        """Získa prvých N textových súborov"""
        prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        
        if not os.path.exists(prepisy_dir):
            return []
        
        txt_files = []
        for file in sorted(os.listdir(prepisy_dir)):
            if file.endswith('_cleaned.txt'):
                txt_files.append(os.path.join(prepisy_dir, file))
                if len(txt_files) >= max_files:
                    break
        
        return txt_files
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def correct_text_with_openai(self, text, episode_name):
        """Opraví text pomocou OpenAI API"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 10000:
                text = text[:10000] + "\n\n[Text skrátený kvôli limitom API]"
            
            prompt = f"""Oprav tento slovenský text z horor podcastu "Krvavý Dobšinský". Zachovaj atmosféru a štýl.

ÚLOHY:
1. Oprav gramatické chyby a preklepy
2. Oprav diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - slovenské úvodzovky „" a pomlčky –
4. Rozdeľ na logické odseky
5. Odstráň výplňové slová (ehm, no, tak, teda, atď.)
6. Zachovaj obsah a štýl
7. Vráť len opravený text

EPIZÓDA: {episode_name}

TEXT:
{text}"""
            
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk. Opravuješ texty z horor podcastu s maximálnou presnosťou."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 3000,
                "temperature": 0.2
            }
            
            print(f"📡 Posielam do OpenAI...")
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                corrected_text = result['choices'][0]['message']['content'].strip()
                return corrected_text
            else:
                print(f"❌ API chyba: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, original_file_path, episode_name):
        """Uloží opravený text"""
        try:
            output_file = os.path.join(self.corrected_dir, f"{episode_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {episode_name}",
                "=" * 70,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4o-mini",
                "Kvalita: Profesionálna slovenčina",
                "",
                corrected_text,
                "",
                "",
                "=" * 70,
                "INFORMÁCIE:",
                f"- Dátum: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4o-mini API",
                f"- Pôvodný súbor: {os.path.basename(original_file_path)}",
                f"- Dĺžka: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika, interpunkcia",
                "- Jazyk: slovenčina (SK) - profesionálna úroveň"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ Uložené: {os.path.basename(output_file)}")
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_file(self, file_path):
        """Spracuje jeden súbor"""
        episode_name = os.path.basename(file_path).replace('_cleaned.txt', '')
        
        print(f"\n📄 === {episode_name} ===")
        
        # Kontrola či už existuje
        existing_file = os.path.join(self.corrected_dir, f"{episode_name}_OpenAI_corrected.txt")
        if os.path.exists(existing_file):
            print(f"⏭️  Už existuje, preskakujem")
            return True
        
        # Extrakcia textu
        story_text = self.extract_story_text(file_path)
        if not story_text:
            return False
        
        print(f"📖 Text: {len(story_text)} znakov")
        
        # Oprava
        corrected_text = self.correct_text_with_openai(story_text, episode_name)
        if not corrected_text:
            return False
        
        print(f"🔧 Opravené: {len(corrected_text)} znakov")
        
        # Uloženie
        output_file = self.save_corrected_text(corrected_text, file_path, episode_name)
        return output_file is not None

def main():
    """Hlavná funkcia"""
    print("🤖 === AUTOMATICKÁ OPRAVA CEZ OPENAI ===")
    print()
    
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = AutoOpenAICorrector(api_key)
    
    # Získanie súborov
    txt_files = corrector.get_text_files(max_files=3)
    
    if not txt_files:
        print("❌ Nenašli sa žiadne súbory!")
        return
    
    print(f"🎯 Spracovávam prvých {len(txt_files)} súborov:")
    for i, file_path in enumerate(txt_files, 1):
        episode_name = os.path.basename(file_path).replace('_cleaned.txt', '')
        print(f"   {i}. {episode_name}")
    
    print(f"\n📁 Výstup: {corrector.corrected_dir}")
    print()
    
    # Spracovanie
    successful = 0
    failed = 0
    
    for i, file_path in enumerate(txt_files, 1):
        print(f"[{i}/{len(txt_files)}] " + "="*50)
        
        if corrector.process_file(file_path):
            successful += 1
        else:
            failed += 1
        
        # Pauza medzi požiadavkami
        if i < len(txt_files):
            print("⏳ Pauza 3 sekundy...")
            time.sleep(3)
    
    print(f"\n🏁 === VÝSLEDOK ===")
    print(f"✅ Úspešne: {successful}")
    print(f"❌ Neúspešne: {failed}")
    print(f"📁 Súbory: {corrector.corrected_dir}")
    
    # Zobrazenie obsahu priečinka
    try:
        files = [f for f in os.listdir(corrector.corrected_dir) if f.endswith('_OpenAI_corrected.txt')]
        print(f"\n📋 OpenAI opravené súbory ({len(files)}):")
        for i, file in enumerate(files, 1):
            print(f"   {i}. {file}")
    except:
        pass

if __name__ == "__main__":
    main()
