#!/usr/bin/env python3
"""
Vytvorí skript pre prepísanie všetkých audio súborov
"""

import os

def create_transcription_script():
    """Vytvorí Python skript pre prepísanie všetkých súborov"""
    
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    script_path = os.path.join(output_dir, "transcribe_all.py")
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    for file in os.listdir(audio_dir):
        if file.endswith('.mp3'):
            audio_files.append(os.path.join(audio_dir, file))
    
    # Vytvorenie skriptu
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write('#!/usr/bin/env python3\n')
        f.write('"""\n')
        f.write('Automatický skript pre prepísanie všetkých audio súborov\n')
        f.write('Vyžaduje internetové pripojenie a SpeechRecognition knižnicu\n')
        f.write('"""\n\n')
        
        f.write('import speech_recognition as sr\n')
        f.write('import subprocess\n')
        f.write('import os\n')
        f.write('import time\n\n')
        
        f.write('def transcribe_file(mp3_path, output_path):\n')
        f.write('    """Prepíše jeden MP3 súbor"""\n')
        f.write('    try:\n')
        f.write('        # Konverzia na WAV\n')
        f.write('        wav_path = "/tmp/temp_audio.wav"\n')
        f.write('        result = subprocess.run([\n')
        f.write('            "afconvert", "-f", "WAVE", "-d", "LEI16@16000", "-c", "1",\n')
        f.write('            mp3_path, wav_path\n')
        f.write('        ], capture_output=True)\n')
        f.write('        \n')
        f.write('        if result.returncode != 0:\n')
        f.write('            print(f"✗ Konverzia zlyhala: {os.path.basename(mp3_path)}")\n')
        f.write('            return False\n')
        f.write('        \n')
        f.write('        # Rozpoznávanie reči\n')
        f.write('        r = sr.Recognizer()\n')
        f.write('        r.energy_threshold = 300\n')
        f.write('        \n')
        f.write('        with sr.AudioFile(wav_path) as source:\n')
        f.write('            r.adjust_for_ambient_noise(source, duration=1)\n')
        f.write('            audio = r.record(source)\n')
        f.write('        \n')
        f.write('        # Pokus o rozpoznanie v slovenčine\n')
        f.write('        try:\n')
        f.write('            text = r.recognize_google(audio, language="sk-SK")\n')
        f.write('        except:\n')
        f.write('            try:\n')
        f.write('                text = r.recognize_google(audio, language="cs-CZ")\n')
        f.write('            except:\n')
        f.write('                text = "[Nepodarilo sa rozpoznať reč]"\n')
        f.write('        \n')
        f.write('        # Uloženie prepisu\n')
        f.write('        with open(output_path, "w", encoding="utf-8") as f:\n')
        f.write('            f.write(f"PREPIS EPIZÓDY: {os.path.splitext(os.path.basename(mp3_path))[0]}\\n")\n')
        f.write('            f.write("=" * 60 + "\\n\\n")\n')
        f.write('            f.write(text)\n')
        f.write('            f.write(f"\\n\\n--- Koniec prepisu ---\\n")\n')
        f.write('            f.write(f"Spracované: {time.strftime(\'%Y-%m-%d %H:%M:%S\')}\\n")\n')
        f.write('        \n')
        f.write('        # Vymazanie dočasného súboru\n')
        f.write('        if os.path.exists(wav_path):\n')
        f.write('            os.remove(wav_path)\n')
        f.write('        \n')
        f.write('        return True\n')
        f.write('        \n')
        f.write('    except Exception as e:\n')
        f.write('        print(f"✗ Chyba: {e}")\n')
        f.write('        return False\n\n')
        
        f.write('def main():\n')
        f.write('    """Hlavná funkcia"""\n')
        f.write(f'    output_dir = "{output_dir}"\n')
        f.write('    os.makedirs(output_dir, exist_ok=True)\n')
        f.write('    \n')
        f.write('    print("=== Automatický prepísač podcastu Krvavý Dobšinský ===")\n')
        f.write(f'    print("Spracovávam {len(audio_files)} súborov...")\n')
        f.write('    print()\n')
        f.write('    \n')
        f.write('    successful = 0\n')
        f.write('    failed = 0\n')
        f.write('    \n')
        
        # Pridanie všetkých súborov
        for i, audio_file in enumerate(audio_files):
            base_name = os.path.splitext(os.path.basename(audio_file))[0]
            output_file = os.path.join(output_dir, f"{base_name}_prepis.txt")
            
            f.write(f'    # Súbor {i+1}/{len(audio_files)}: {base_name}\n')
            f.write(f'    print("[{i+1}/{len(audio_files)}] {base_name}")\n')
            f.write(f'    if transcribe_file("{audio_file}", "{output_file}"):\n')
            f.write('        print("✓ Úspešne prepísané")\n')
            f.write('        successful += 1\n')
            f.write('    else:\n')
            f.write('        print("✗ Neúspešné")\n')
            f.write('        failed += 1\n')
            f.write('    print()\n')
            f.write('    time.sleep(2)  # Pauza medzi súbormi\n')
            f.write('    \n')
        
        f.write('    print(f"\\nHotovo!")\n')
        f.write('    print(f"Úspešne prepísané: {successful}")\n')
        f.write('    print(f"Neúspešné: {failed}")\n')
        f.write('    print(f"Prepisy sú v: {output_dir}")\n\n')
        
        f.write('if __name__ == "__main__":\n')
        f.write('    main()\n')
    
    # Nastavenie práv na spustenie
    os.chmod(script_path, 0o755)
    
    print(f"✓ Skript vytvorený: {script_path}")
    print(f"✓ Bude spracovávať {len(audio_files)} súborov")
    print("\nPre spustenie:")
    print(f"cd '{output_dir}'")
    print("python3 transcribe_all.py")

if __name__ == "__main__":
    create_transcription_script()
