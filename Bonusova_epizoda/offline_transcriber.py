#!/usr/bin/env python3
"""
Offline skript na prepísanie textu z audio súborov
Používa lokálne nástroje bez potreby internetu
"""

import os
import subprocess
import time
from pathlib import Path

def convert_mp3_to_wav(mp3_file_path):
    """Konvertuje MP3 na WAV pomocou afconvert (macOS)"""
    try:
        wav_path = mp3_file_path.replace('.mp3', '_converted.wav')
        
        # Použitie afconvert (macOS nástroj)
        result = subprocess.run([
            'afconvert', 
            '-f', 'WAVE', 
            '-d', 'LEI16@16000',  # 16-bit little endian, 16kHz
            '-c', '1',  # mono
            mp3_file_path, 
            wav_path
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(wav_path):
            print(f"✓ Konvertované na WAV: {os.path.basename(wav_path)}")
            return wav_path
        else:
            print(f"✗ Konverzia zlyhala: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"✗ Chyba pri konverzii: {e}")
        return None

def extract_audio_info(audio_file_path):
    """Extrahuje informácie o audio súbore"""
    try:
        # Použitie afinfo (macOS nástroj)
        result = subprocess.run([
            'afinfo', audio_file_path
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            info = result.stdout
            duration = "Neznáma"
            
            # Hľadanie dĺžky súboru
            for line in info.split('\n'):
                if 'estimated duration:' in line.lower():
                    duration = line.split(':')[1].strip()
                    break
            
            return {
                'duration': duration,
                'info': info
            }
        else:
            return {'duration': 'Neznáma', 'info': 'Nedostupné'}
            
    except Exception as e:
        return {'duration': 'Chyba', 'info': str(e)}

def create_basic_transcript(audio_file_path, output_dir):
    """Vytvorí základný prepis s informáciami o súbore"""
    print(f"\nSpracovávam: {os.path.basename(audio_file_path)}")
    
    try:
        # Získanie informácií o súbore
        audio_info = extract_audio_info(audio_file_path)
        
        # Konverzia na WAV pre analýzu
        wav_path = convert_mp3_to_wav(audio_file_path)
        
        # Vytvorenie názvu výstupného súboru
        base_name = os.path.splitext(os.path.basename(audio_file_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_info.txt")
        
        # Vytvorenie základného prepisu s informáciami
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"INFORMÁCIE O EPIZÓDE: {base_name}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Názov súboru: {os.path.basename(audio_file_path)}\n")
            f.write(f"Dĺžka: {audio_info['duration']}\n")
            f.write(f"Veľkosť súboru: {os.path.getsize(audio_file_path) / (1024*1024):.2f} MB\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("TECHNICKÉ INFORMÁCIE:\n")
            f.write("-" * 30 + "\n")
            f.write(audio_info['info'])
            f.write("\n\n")
            
            f.write("POZNÁMKA:\n")
            f.write("-" * 10 + "\n")
            f.write("Pre plný prepis textu je potrebné použiť online služby rozpoznávania reči.\n")
            f.write("Tento súbor obsahuje len základné informácie o audio súbore.\n")
            f.write("Audio súbor je pripravený na ďalšie spracovanie.\n\n")
            
            if wav_path and os.path.exists(wav_path):
                f.write(f"WAV verzia vytvorená: {os.path.basename(wav_path)}\n")
                f.write("Súbor je pripravený na prepis pomocou externých nástrojov.\n")
        
        print(f"✓ Informácie uložené: {output_file}")
        
        # Vymazanie dočasného WAV súboru
        if wav_path and os.path.exists(wav_path):
            os.remove(wav_path)
            print(f"Vymazaný dočasný súbor: {os.path.basename(wav_path)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní {audio_file_path}: {e}")
        return False

def create_batch_script(audio_files, output_dir):
    """Vytvorí batch skript pre hromadné spracovanie"""
    script_path = os.path.join(output_dir, "batch_transcribe.sh")
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write("#!/bin/bash\n")
        f.write("# Batch skript pre prepísanie audio súborov\n")
        f.write("# Vyžaduje internetové pripojenie a Google Speech Recognition\n\n")
        
        for audio_file in audio_files:
            base_name = os.path.splitext(os.path.basename(audio_file))[0]
            f.write(f'echo "Spracovávam: {base_name}"\n')
            f.write(f'python3 -c "\n')
            f.write(f'import speech_recognition as sr\n')
            f.write(f'import subprocess\n')
            f.write(f'import os\n')
            f.write(f'# Konverzia na WAV\n')
            f.write(f'subprocess.run([\"afconvert\", \"-f\", \"WAVE\", \"-d\", \"LEI16@16000\", \"-c\", \"1\", \"{audio_file}\", \"/tmp/temp.wav\"])\n')
            f.write(f'# Rozpoznávanie reči\n')
            f.write(f'r = sr.Recognizer()\n')
            f.write(f'with sr.AudioFile(\"/tmp/temp.wav\") as source:\n')
            f.write(f'    audio = r.record(source)\n')
            f.write(f'try:\n')
            f.write(f'    text = r.recognize_google(audio, language=\"sk-SK\")\n')
            f.write(f'    with open(\"{output_dir}/{base_name}_prepis.txt\", \"w\", encoding=\"utf-8\") as f:\n')
            f.write(f'        f.write(\"PREPIS: {base_name}\\n\\n\" + text)\n')
            f.write(f'    print(\"✓ Úspešne prepísané\")\n')
            f.write(f'except:\n')
            f.write(f'    print(\"✗ Chyba pri prepise\")\n')
            f.write(f'os.remove(\"/tmp/temp.wav\")\n')
            f.write(f'"\n\n')
    
    # Nastavenie práv na spustenie
    os.chmod(script_path, 0o755)
    print(f"✓ Batch skript vytvorený: {script_path}")

def main():
    """Hlavná funkcia"""
    # Cesty
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    # Vytvorenie adresára pre prepisy
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== Offline prepísač audio súborov Krvavý Dobšinský ===")
    print(f"Zdrojový adresár: {audio_dir}")
    print(f"Výstupný adresár: {output_dir}")
    print()
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    for file in os.listdir(audio_dir):
        if file.endswith('.mp3'):
            audio_files.append(os.path.join(audio_dir, file))
    
    if not audio_files:
        print("Nenašli sa žiadne MP3 súbory!")
        return
    
    print(f"Nájdených {len(audio_files)} audio súborov")
    
    # Spýtanie sa používateľa, čo chce robiť
    print("\nMôžete:")
    print("1. Vytvoriť informačné súbory pre všetky epizódy")
    print("2. Vytvoriť informačné súbory pre prvých 5 epizód")
    print("3. Vytvoriť batch skript pre online prepis")
    print("4. Zobraziť zoznam všetkých epizód")
    
    choice = input("\nVyberte možnosť (1/2/3/4): ").strip()
    
    if choice == "1":
        print("\nSpracovávam všetky súbory...")
        successful = 0
        for audio_file in audio_files:
            if create_basic_transcript(audio_file, output_dir):
                successful += 1
            time.sleep(1)
        print(f"\n✓ Spracovaných: {successful}/{len(audio_files)} súborov")
        
    elif choice == "2":
        print("\nSpracovávam prvých 5 súborov...")
        successful = 0
        for audio_file in audio_files[:5]:
            if create_basic_transcript(audio_file, output_dir):
                successful += 1
            time.sleep(1)
        print(f"\n✓ Spracovaných: {successful}/5 súborov")
        
    elif choice == "3":
        print("\nVytvárám batch skript...")
        create_batch_script(audio_files, output_dir)
        print("Batch skript môžete spustiť neskôr keď budete mať internetové pripojenie.")
        
    elif choice == "4":
        print("\nZoznam všetkých epizód:")
        print("-" * 50)
        for i, audio_file in enumerate(audio_files, 1):
            name = os.path.splitext(os.path.basename(audio_file))[0]
            size = os.path.getsize(audio_file) / (1024*1024)
            print(f"{i:2d}. {name} ({size:.1f} MB)")
        
    else:
        print("Neplatná voľba!")
        return
    
    print(f"\nVýsledky sú uložené v: {output_dir}")

if __name__ == "__main__":
    main()
