#!/usr/bin/env python3
"""
Prepíše jednu konkrétnu epizódu podcastu Krvavý Dobšinský
Rozdelí dlhý audio súbor na menšie časti pre lepšie rozpoznávanie
"""

import os
import speech_recognition as sr
import subprocess
import time
import tempfile
from pathlib import Path

def convert_mp3_to_wav(mp3_path, output_wav_path):
    """Konvertuje MP3 na WAV pomocí afconvert"""
    try:
        result = subprocess.run([
            'afconvert', 
            '-f', 'WAVE', 
            '-d', 'LEI16@16000',  # 16-bit, 16kHz
            '-c', '1',  # mono
            mp3_path, 
            output_wav_path
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ Konvertované na WAV: {os.path.basename(output_wav_path)}")
            return True
        else:
            print(f"✗ Konverzia zlyhala: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Chyba pri konverzii: {e}")
        return False

def split_audio_file(wav_path, chunk_duration=30):
    """Rozdelí WAV súbor na menšie časti"""
    try:
        # Získanie dĺžky súboru
        result = subprocess.run([
            'afinfo', wav_path
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("✗ Nepodarilo sa získať informácie o súbore")
            return []
        
        # Hľadanie dĺžky v sekundách
        duration_seconds = None
        for line in result.stdout.split('\n'):
            if 'estimated duration:' in line.lower():
                duration_str = line.split(':')[1].strip().split()[0]
                try:
                    duration_seconds = float(duration_str)
                    break
                except:
                    continue
        
        if not duration_seconds:
            print("✗ Nepodarilo sa určiť dĺžku súboru")
            return []
        
        print(f"Dĺžka súboru: {duration_seconds:.1f} sekúnd")
        
        # Rozdelenie na časti
        chunks = []
        chunk_count = int(duration_seconds / chunk_duration) + 1
        
        print(f"Rozdeľujem na {chunk_count} častí po {chunk_duration} sekúnd...")
        
        for i in range(chunk_count):
            start_time = i * chunk_duration
            chunk_path = f"/tmp/chunk_{i:03d}.wav"
            
            # Použitie afconvert na vyrezanie časti
            result = subprocess.run([
                'afconvert',
                '-f', 'WAVE',
                '-d', 'LEI16@16000',
                '-c', '1',
                '--src-start-time', str(start_time),
                '--src-duration', str(chunk_duration),
                wav_path,
                chunk_path
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and os.path.exists(chunk_path):
                chunks.append(chunk_path)
                print(f"  ✓ Časť {i+1}/{chunk_count} vytvorená")
            else:
                print(f"  ✗ Chyba pri vytváraní časti {i+1}")
        
        return chunks
        
    except Exception as e:
        print(f"✗ Chyba pri rozdeľovaní súboru: {e}")
        return []

def transcribe_audio_chunk(chunk_path, chunk_number):
    """Prepíše jednu časť audio súboru"""
    try:
        recognizer = sr.Recognizer()
        recognizer.energy_threshold = 300
        recognizer.dynamic_energy_threshold = True
        recognizer.pause_threshold = 0.8
        
        with sr.AudioFile(chunk_path) as source:
            print(f"    Načítavam časť {chunk_number}...")
            recognizer.adjust_for_ambient_noise(source, duration=0.5)
            audio_data = recognizer.record(source)
        
        # Pokus o rozpoznanie v rôznych jazykoch
        languages = ['sk-SK', 'cs-CZ', 'en-US']
        
        for lang in languages:
            try:
                print(f"    Rozpoznávam v jazyku {lang}...")
                text = recognizer.recognize_google(audio_data, language=lang)
                print(f"    ✓ Úspešne rozpoznané v {lang}")
                return text
            except sr.UnknownValueError:
                continue
            except sr.RequestError as e:
                print(f"    ✗ Chyba služby pre {lang}: {e}")
                continue
        
        return "[Nepodarilo sa rozpoznať reč v tejto časti]"
        
    except Exception as e:
        print(f"    ✗ Chyba pri prepise časti {chunk_number}: {e}")
        return f"[Chyba pri spracovaní časti {chunk_number}: {e}]"

def transcribe_complete_episode(mp3_path, output_dir):
    """Prepíše kompletnú epizódu"""
    episode_name = os.path.splitext(os.path.basename(mp3_path))[0]
    print(f"\n=== PREPÍSANIE EPIZÓDY: {episode_name} ===")
    
    # Vytvorenie dočasného WAV súboru
    temp_wav = f"/tmp/{episode_name}_temp.wav"
    
    try:
        # 1. Konverzia MP3 na WAV
        print("\n1. Konvertujem MP3 na WAV...")
        if not convert_mp3_to_wav(mp3_path, temp_wav):
            return False
        
        # 2. Rozdelenie na časti
        print("\n2. Rozdeľujem na menšie časti...")
        chunks = split_audio_file(temp_wav, chunk_duration=30)
        
        if not chunks:
            print("✗ Nepodarilo sa rozdeliť súbor")
            return False
        
        # 3. Prepísanie každej časti
        print(f"\n3. Prepísujem {len(chunks)} častí...")
        full_transcript = []
        
        for i, chunk_path in enumerate(chunks, 1):
            print(f"\n  Časť {i}/{len(chunks)}:")
            text = transcribe_audio_chunk(chunk_path, i)
            
            # Pridanie časovej značky
            start_time = (i-1) * 30
            minutes = start_time // 60
            seconds = start_time % 60
            timestamp = f"[{minutes:02d}:{seconds:02d}]"
            
            full_transcript.append(f"{timestamp} {text}")
            
            # Vymazanie dočasnej časti
            if os.path.exists(chunk_path):
                os.remove(chunk_path)
            
            # Krátka pauza medzi časťami
            time.sleep(1)
        
        # 4. Uloženie kompletného prepisu
        print("\n4. Ukladám kompletný prepis...")
        output_file = os.path.join(output_dir, f"{episode_name}_kompletny_prepis.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"KOMPLETNÝ PREPIS EPIZÓDY: {episode_name}\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Súbor: {os.path.basename(mp3_path)}\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Počet častí: {len(chunks)}\n\n")
            f.write("PREPIS:\n")
            f.write("-" * 40 + "\n\n")
            
            for line in full_transcript:
                f.write(line + "\n\n")
            
            f.write("\n" + "=" * 80 + "\n")
            f.write("KONIEC PREPISU\n")
        
        print(f"✓ Kompletný prepis uložený: {output_file}")
        
        # Vymazanie dočasného WAV súboru
        if os.path.exists(temp_wav):
            os.remove(temp_wav)
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní epizódy: {e}")
        
        # Vyčistenie dočasných súborov
        if os.path.exists(temp_wav):
            os.remove(temp_wav)
        
        return False

def main():
    """Hlavná funkcia"""
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== PREPÍSANIE JEDNEJ KOMPLETNEJ EPIZÓDY ===")
    print(f"Zdrojový adresár: {audio_dir}")
    print(f"Výstupný adresár: {output_dir}")
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    try:
        for file in os.listdir(audio_dir):
            if file.endswith('.mp3'):
                audio_files.append(os.path.join(audio_dir, file))
    except Exception as e:
        print(f"✗ Chyba pri načítaní súborov: {e}")
        return
    
    if not audio_files:
        print("✗ Nenašli sa žiadne MP3 súbory!")
        return
    
    print(f"\nNájdených {len(audio_files)} epizód")
    
    # Zobrazenie prvých 10 epizód
    print("\nDostupné epizódy:")
    for i, file in enumerate(audio_files[:10]):
        name = os.path.splitext(os.path.basename(file))[0]
        size = os.path.getsize(file) / (1024*1024)
        print(f"{i+1:2d}. {name} ({size:.1f} MB)")
    
    if len(audio_files) > 10:
        print(f"    ... a ďalších {len(audio_files)-10} epizód")
    
    # Výber epizódy
    try:
        choice = input(f"\nVyberte číslo epizódy (1-{min(10, len(audio_files))}): ").strip()
        episode_index = int(choice) - 1
        
        if 0 <= episode_index < min(10, len(audio_files)):
            selected_file = audio_files[episode_index]
            print(f"\nVybraná epizóda: {os.path.basename(selected_file)}")
            
            # Prepísanie vybranej epizódy
            success = transcribe_complete_episode(selected_file, output_dir)
            
            if success:
                print(f"\n🎉 ÚSPECH! Epizóda bola kompletne prepísaná.")
                print(f"📁 Prepis nájdete v: {output_dir}")
            else:
                print(f"\n❌ CHYBA! Nepodarilo sa prepísať epizódu.")
        else:
            print("✗ Neplatné číslo epizódy!")
    
    except ValueError:
        print("✗ Neplatný vstup!")
    except KeyboardInterrupt:
        print("\n\n⏹️  Prerušené používateľom")

if __name__ == "__main__":
    main()
