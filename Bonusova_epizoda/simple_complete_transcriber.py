#!/usr/bin/env python3
"""
Jednoduchý prepísač celej epizódy naraz
"""

import os
import speech_recognition as sr
import subprocess
import time

def convert_and_transcribe_episode(mp3_path, output_dir):
    """Konvertuje a prepíše celú epizódu"""
    episode_name = os.path.splitext(os.path.basename(mp3_path))[0]
    print(f"\n=== PREPÍSANIE EPIZÓDY: {episode_name} ===")
    
    # Dočasný WAV súbor
    temp_wav = f"/tmp/{episode_name.replace(' ', '_')}_temp.wav"
    
    try:
        # 1. Konverzia MP3 na WAV
        print("\n1. Konvertujem MP3 na WAV...")
        result = subprocess.run([
            'afconvert', 
            '-f', 'WAVE', 
            '-d', 'LEI16@16000',  # 16-bit, 16kHz
            '-c', '1',  # mono
            mp3_path, 
            temp_wav
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"✗ Konverzia zlyhala: {result.stderr}")
            return False
        
        print(f"✓ Konvertované na WAV")
        
        # 2. Získanie informácií o súbore
        print("\n2. Získavam informácie o súbore...")
        info_result = subprocess.run(['afinfo', temp_wav], capture_output=True, text=True)
        
        duration = "Neznáma"
        if info_result.returncode == 0:
            for line in info_result.stdout.split('\n'):
                if 'estimated duration:' in line.lower():
                    duration = line.split(':')[1].strip()
                    break
        
        print(f"Dĺžka súboru: {duration}")
        
        # 3. Rozpoznávanie reči
        print("\n3. Rozpoznávam reč...")
        recognizer = sr.Recognizer()
        recognizer.energy_threshold = 300
        recognizer.dynamic_energy_threshold = True
        recognizer.pause_threshold = 0.8
        recognizer.phrase_threshold = 0.3
        
        with sr.AudioFile(temp_wav) as source:
            print("   Načítavam audio súbor...")
            recognizer.adjust_for_ambient_noise(source, duration=2)
            print("   Načítavam audio dáta...")
            audio_data = recognizer.record(source)
        
        print("   Audio dáta načítané, začínam rozpoznávanie...")
        
        # Pokus o rozpoznanie v rôznych jazykoch
        languages = [
            ('sk-SK', 'slovenčina'),
            ('cs-CZ', 'čeština'), 
            ('en-US', 'angličtina')
        ]
        
        transcript = None
        used_language = None
        
        for lang_code, lang_name in languages:
            try:
                print(f"   Skúšam rozpoznávanie v jazyku: {lang_name} ({lang_code})")
                transcript = recognizer.recognize_google(audio_data, language=lang_code)
                used_language = lang_name
                print(f"   ✓ Úspešne rozpoznané v jazyku: {lang_name}")
                break
            except sr.UnknownValueError:
                print(f"   ✗ Nerozpoznané v jazyku: {lang_name}")
                continue
            except sr.RequestError as e:
                print(f"   ✗ Chyba služby pre {lang_name}: {e}")
                continue
            except Exception as e:
                print(f"   ✗ Neočakávaná chyba pre {lang_name}: {e}")
                continue
        
        if not transcript:
            transcript = "[Nepodarilo sa rozpoznať reč v žiadnom podporovanom jazyku]"
            used_language = "žiadny"
        
        # 4. Uloženie prepisu
        print("\n4. Ukladám prepis...")
        output_file = os.path.join(output_dir, f"{episode_name}_kompletny_prepis.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"KOMPLETNÝ PREPIS EPIZÓDY\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Názov epizódy: {episode_name}\n")
            f.write(f"Súbor: {os.path.basename(mp3_path)}\n")
            f.write(f"Dĺžka: {duration}\n")
            f.write(f"Rozpoznané v jazyku: {used_language}\n")
            f.write(f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("PREPIS:\n")
            f.write("-" * 80 + "\n\n")
            f.write(transcript)
            f.write("\n\n" + "-" * 80 + "\n")
            f.write("KONIEC PREPISU\n")
        
        print(f"✓ Prepis uložený: {output_file}")
        
        # Vymazanie dočasného súboru
        if os.path.exists(temp_wav):
            os.remove(temp_wav)
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba pri spracovaní: {e}")
        
        # Vyčistenie dočasných súborov
        if os.path.exists(temp_wav):
            os.remove(temp_wav)
        
        return False

def main():
    """Hlavná funkcia"""
    audio_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    output_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== JEDNODUCHÝ PREPÍSAČ KOMPLETNEJ EPIZÓDY ===")
    print(f"Zdrojový adresár: {audio_dir}")
    print(f"Výstupný adresár: {output_dir}")
    print("\nPOZNÁMKA: Tento skript prepíše celú epizódu naraz.")
    print("Pre dlhé epizódy môže trvať dlho alebo zlyhať kvôli obmedzeniam Google Speech API.")
    
    # Nájdenie všetkých MP3 súborov
    audio_files = []
    try:
        for file in os.listdir(audio_dir):
            if file.endswith('.mp3'):
                audio_files.append(os.path.join(audio_dir, file))
    except Exception as e:
        print(f"✗ Chyba pri načítaní súborov: {e}")
        return
    
    if not audio_files:
        print("✗ Nenašli sa žiadne MP3 súbory!")
        return
    
    # Zoradenie súborov podľa veľkosti (menšie najprv)
    audio_files.sort(key=lambda x: os.path.getsize(x))
    
    print(f"\nNájdených {len(audio_files)} epizód (zoradené podľa veľkosti)")
    
    # Zobrazenie prvých 10 epizód
    print("\nDostupné epizódy (odporúčame začať s menšími):")
    for i, file in enumerate(audio_files[:10]):
        name = os.path.splitext(os.path.basename(file))[0]
        size = os.path.getsize(file) / (1024*1024)
        print(f"{i+1:2d}. {name} ({size:.1f} MB)")
    
    if len(audio_files) > 10:
        print(f"    ... a ďalších {len(audio_files)-10} epizód")
    
    # Výber epizódy
    try:
        choice = input(f"\nVyberte číslo epizódy (1-{min(10, len(audio_files))}): ").strip()
        episode_index = int(choice) - 1
        
        if 0 <= episode_index < min(10, len(audio_files)):
            selected_file = audio_files[episode_index]
            file_size = os.path.getsize(selected_file) / (1024*1024)
            
            print(f"\nVybraná epizóda: {os.path.basename(selected_file)}")
            print(f"Veľkosť súboru: {file_size:.1f} MB")
            
            if file_size > 25:
                print("⚠️  UPOZORNENIE: Tento súbor je veľký a prepis môže trvať dlho alebo zlyhať!")
                confirm = input("Chcete pokračovať? (y/n): ").strip().lower()
                if confirm not in ['y', 'yes', 'ano', 'áno']:
                    print("Zrušené používateľom.")
                    return
            
            print("\n🚀 Začínam prepísanie...")
            
            # Prepísanie vybranej epizódy
            success = convert_and_transcribe_episode(selected_file, output_dir)
            
            if success:
                print(f"\n🎉 ÚSPECH! Epizóda bola kompletne prepísaná.")
                print(f"📁 Prepis nájdete v: {output_dir}")
            else:
                print(f"\n❌ CHYBA! Nepodarilo sa prepísať epizódu.")
                print("💡 Skúste menší súbor alebo skontrolujte internetové pripojenie.")
        else:
            print("✗ Neplatné číslo epizódy!")
    
    except ValueError:
        print("✗ Neplatný vstup!")
    except KeyboardInterrupt:
        print("\n\n⏹️  Prerušené používateľom")

if __name__ == "__main__":
    main()
