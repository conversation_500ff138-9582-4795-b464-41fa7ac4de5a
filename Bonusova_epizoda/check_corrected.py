#!/usr/bin/env python3
"""
Kontrola súborov v priečinku corrected
"""

import os

def check_corrected_folder():
    """Skontroluje obsah priečinka corrected"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    
    print("🔍 === KONTROLA PRIEČINKA CORRECTED ===")
    print(f"📁 Cesta: {corrected_dir}")
    print()
    
    if not os.path.exists(corrected_dir):
        print("❌ Priečinok neexistuje!")
        return
    
    try:
        files = os.listdir(corrected_dir)
        
        if not files:
            print("📂 Priečinok je prázdny")
            return
        
        print(f"📋 Nájdených {len(files)} súborov:")
        print()
        
        for i, file in enumerate(files, 1):
            file_path = os.path.join(corrected_dir, file)
            
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                size_kb = size / 1024
                
                print(f"{i:2d}. {file}")
                print(f"    Veľkosť: {size_kb:.1f} KB ({size} bytov)")
                
                # Ukážka obsahu pre textové súbory
                if file.endswith('.txt'):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            preview = content[:100].replace('\n', ' ')
                            print(f"    Ukážka: {preview}...")
                    except:
                        print(f"    Ukážka: [Nepodarilo sa načítať]")
                
                print()
            else:
                print(f"{i:2d}. {file} (priečinok)")
                print()
        
        # Štatistiky
        txt_files = [f for f in files if f.endswith('.txt')]
        print(f"📊 ŠTATISTIKY:")
        print(f"   Celkom súborov: {len(files)}")
        print(f"   TXT súbory: {len(txt_files)}")
        print(f"   Ostatné: {len(files) - len(txt_files)}")
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní priečinka: {e}")

def show_specific_file(filename):
    """Zobrazí obsah konkrétneho súboru"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    file_path = os.path.join(corrected_dir, filename)
    
    print(f"\n📄 === OBSAH SÚBORU: {filename} ===")
    
    if not os.path.exists(file_path):
        print(f"❌ Súbor {filename} neexistuje!")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 Veľkosť: {len(content)} znakov")
        print("📖 Obsah:")
        print("-" * 60)
        print(content[:1000])  # Prvých 1000 znakov
        if len(content) > 1000:
            print("...")
            print(f"[Zobrazených prvých 1000 z {len(content)} znakov]")
        print("-" * 60)
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní súboru: {e}")

def main():
    """Hlavná funkcia"""
    check_corrected_folder()
    
    # Zobrazenie konkrétnych súborov
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    
    if os.path.exists(corrected_dir):
        files = [f for f in os.listdir(corrected_dir) if f.endswith('.txt')]
        
        for file in files:
            if 'corrected' in file:
                show_specific_file(file)
                break

if __name__ == "__main__":
    main()
