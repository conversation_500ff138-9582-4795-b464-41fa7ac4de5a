#!/usr/bin/env python3
"""
Komplexný čistič a opravovač textových prepisov podcastu Krvavý Dobšinský
Opravuje gramatiku, pravopis, formátovanie a štruktúru
"""

import os
import re
import time
from pathlib import Path

class TextCleaner:
    def __init__(self):
        self.filler_words = [
            'ehm', 'ehm,', 'ehm.', 'ähm', 'äh', 'eh', 'eh,', 'eh.',
            'hmm', 'hmm,', 'hmm.', 'hm', 'hm,', 'hm.',
            'no', 'no,', 'no.', 'noo', 'noo,', 'noo.',
            'aha', 'aha,', 'aha.', 'ahá', 'ahá,', 'ahá.',
            'tak', 'tak,', 'takže', 'takže,',
            'vlastne', 'vlastne,', 'vlastne.',
            'teda', 'teda,', 'teda.',
            'čiže', 'čiže,', 'čiže.',
            'proste', 'proste,', 'proste.',
        ]
        
        # Časté chyby z rozpoznávania reči
        self.common_errors = {
            # Slovenské slová často zle rozpoznané
            'dobšinský': 'Dobšinský',
            'krvavý': 'Krvavý',
            'slovensko': 'Slovensko',
            'slovakia': 'Slovensko',
            'bratislava': 'Bratislava',
            'košice': 'Košice',
            'prešov': 'Prešov',
            'žilina': 'Žilina',
            'banská bystrica': 'Banská Bystrica',
            'trenčín': 'Trenčín',
            'nitra': 'Nitra',
            'trnava': 'Trnava',
            
            # Časté gramatické chyby
            ' a ': ' a ',
            ' v ': ' v ',
            ' s ': ' s ',
            ' z ': ' z ',
            ' k ': ' k ',
            ' o ': ' o ',
            ' na ': ' na ',
            ' za ': ' za ',
            ' pre ': ' pre ',
            ' pri ': ' pri ',
            ' po ': ' po ',
            ' od ': ' od ',
            ' do ': ' do ',
            
            # Oprava interpunkcie
            ' ,': ',',
            ' .': '.',
            ' !': '!',
            ' ?': '?',
            ' :': ':',
            ' ;': ';',
            ',,': ',',
            '..': '.',
            '!!': '!',
            '??': '?',
            
            # Časté chyby v rozpoznávaní
            'nerozpoznané': '[nerozpoznané]',
            'nepodarilo sa rozpoznať': '[nerozpoznané]',
            'recognition connection failed': '[chyba pripojenia]',
            'broken pipe': '[prerušené pripojenie]',
        }

    def clean_filler_words(self, text):
        """Odstráni výplňové slová"""
        for filler in self.filler_words:
            # Odstránenie na začiatku vety
            text = re.sub(r'\b' + re.escape(filler) + r'\s+', '', text, flags=re.IGNORECASE)
            # Odstránenie v strede vety
            text = re.sub(r'\s+' + re.escape(filler) + r'\s+', ' ', text, flags=re.IGNORECASE)
            # Odstránenie na konci vety
            text = re.sub(r'\s+' + re.escape(filler) + r'\b', '', text, flags=re.IGNORECASE)
        
        return text

    def fix_common_errors(self, text):
        """Opraví časté chyby"""
        for error, correction in self.common_errors.items():
            text = text.replace(error, correction)
        
        return text

    def fix_punctuation(self, text):
        """Opraví interpunkciu"""
        # Odstránenie medzier pred interpunkciou
        text = re.sub(r'\s+([,.!?:;])', r'\1', text)
        
        # Pridanie medzery za interpunkciu ak chýba
        text = re.sub(r'([,.!?:;])([a-zA-ZáäčďéíĺľňóôŕšťúýžÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ])', r'\1 \2', text)
        
        # Oprava viacnásobnej interpunkcie
        text = re.sub(r'[,]{2,}', ',', text)
        text = re.sub(r'[.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Oprava medzier
        text = re.sub(r'\s+', ' ', text)  # Viacnásobné medzery
        text = re.sub(r'\n\s+', '\n', text)  # Medzery na začiatku riadku
        text = re.sub(r'\s+\n', '\n', text)  # Medzery na konci riadku
        
        return text

    def add_paragraphs(self, text):
        """Pridá odseky na základe obsahu"""
        # Rozdelenie na vety
        sentences = re.split(r'[.!?]+', text)
        
        if len(sentences) < 3:
            return text
        
        # Pridanie odsekov každých 3-5 viet
        paragraphs = []
        current_paragraph = []
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                current_paragraph.append(sentence)
                
                # Nový odsek každých 3-5 viet alebo pri kľúčových slovách
                if (len(current_paragraph) >= 3 and 
                    (len(current_paragraph) >= 5 or 
                     any(keyword in sentence.lower() for keyword in 
                         ['potom', 'ďalej', 'nakoniec', 'takže', 'ale', 'však', 'preto']))):
                    
                    paragraphs.append('. '.join(current_paragraph) + '.')
                    current_paragraph = []
        
        # Pridanie posledného odseku
        if current_paragraph:
            paragraphs.append('. '.join(current_paragraph) + '.')
        
        return '\n\n'.join(paragraphs)

    def capitalize_sentences(self, text):
        """Opraví veľké písmená na začiatku viet"""
        # Veľké písmeno na začiatku textu
        text = re.sub(r'^([a-záäčďéíĺľňóôŕšťúýž])', lambda m: m.group(1).upper(), text)
        
        # Veľké písmeno po bodke, výkričníku, otázníku
        text = re.sub(r'([.!?]\s+)([a-záäčďéíĺľňóôŕšťúýž])', 
                     lambda m: m.group(1) + m.group(2).upper(), text)
        
        # Veľké písmeno po dvojbodke ak nasleduje veta
        text = re.sub(r'(:\s+)([a-záäčďéíĺľňóôŕšťúýž])', 
                     lambda m: m.group(1) + m.group(2).upper(), text)
        
        return text

    def clean_text(self, text):
        """Hlavná funkcia na čistenie textu"""
        original_length = len(text)
        
        # 1. Odstránenie výplňových slov
        text = self.clean_filler_words(text)
        
        # 2. Oprava častých chýb
        text = self.fix_common_errors(text)
        
        # 3. Oprava interpunkcie
        text = self.fix_punctuation(text)
        
        # 4. Oprava veľkých písmen
        text = self.capitalize_sentences(text)
        
        # 5. Pridanie odsekov
        text = self.add_paragraphs(text)
        
        # 6. Finálne čistenie
        text = text.strip()
        
        cleaned_length = len(text)
        reduction = original_length - cleaned_length
        
        return text, reduction

def process_transcript_file(file_path, cleaner):
    """Spracuje jeden prepis"""
    try:
        # Načítanie súboru
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Rozdelenie na hlavičku a obsah
        lines = content.split('\n')
        header_lines = []
        content_lines = []
        
        # Nájdenie začiatku prepisu
        content_start = 0
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in 
                   ['prepis:', 'prepis', 'text:', 'obsah:', '---', '===']):
                content_start = i + 1
                header_lines = lines[:i+1]
                break
        
        if content_start == 0:
            # Ak nenájdeme hlavičku, celý obsah je prepis
            content_lines = lines
            header_lines = []
        else:
            content_lines = lines[content_start:]
        
        # Spojenie obsahu prepisu
        transcript_text = '\n'.join(content_lines).strip()
        
        if not transcript_text or len(transcript_text) < 10:
            return False, "Prázdny alebo príliš krátky prepis"
        
        # Čistenie textu
        cleaned_text, reduction = cleaner.clean_text(transcript_text)
        
        # Vytvorenie nového obsahu
        new_content = []
        
        # Pridanie hlavičky
        if header_lines:
            new_content.extend(header_lines)
        else:
            # Vytvorenie novej hlavičky
            file_name = os.path.splitext(os.path.basename(file_path))[0]
            new_content.extend([
                f"PREPIS EPIZÓDY: {file_name}",
                "=" * 60,
                f"Spracované: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Automaticky vyčistené a opravené",
                "",
                "PREPIS:",
                "-" * 40,
                ""
            ])
        
        # Pridanie vyčisteného prepisu
        new_content.append(cleaned_text)
        
        # Pridanie informácií o spracovaní
        new_content.extend([
            "",
            "",
            "=" * 60,
            "INFORMÁCIE O SPRACOVANÍ:",
            f"- Automaticky vyčistené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"- Odstránené znaky: {reduction}",
            f"- Pôvodná dĺžka: {len(transcript_text)} znakov",
            f"- Nová dĺžka: {len(cleaned_text)} znakov",
            "- Opravené: gramatika, interpunkcia, štruktúra, výplňové slová"
        ])
        
        # Uloženie vyčisteného súboru
        cleaned_file_path = file_path.replace('.txt', '_cleaned.txt')
        with open(cleaned_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_content))
        
        return True, f"Úspešne vyčistené, odstránené {reduction} znakov"
        
    except Exception as e:
        return False, f"Chyba: {e}"

def main():
    """Hlavná funkcia"""
    text_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("=== ČISTENIE A OPRAVA TEXTOVÝCH PREPISOV ===")
    print(f"Priečinok: {text_dir}")
    print()
    
    if not os.path.exists(text_dir):
        print(f"✗ Priečinok {text_dir} neexistuje!")
        return
    
    # Nájdenie všetkých TXT súborov
    txt_files = []
    for file in os.listdir(text_dir):
        if file.lower().endswith('.txt') and not file.endswith('_cleaned.txt'):
            txt_files.append(os.path.join(text_dir, file))
    
    if not txt_files:
        print("✗ Nenašli sa žiadne TXT súbory na spracovanie!")
        return
    
    print(f"Nájdených {len(txt_files)} súborov na spracovanie")
    print()
    
    # Vytvorenie cleanera
    cleaner = TextCleaner()
    
    # Spracovanie každého súboru
    successful = 0
    failed = 0
    total_reduction = 0
    
    for i, file_path in enumerate(txt_files, 1):
        file_name = os.path.basename(file_path)
        print(f"[{i:2d}/{len(txt_files)}] {file_name}")
        
        success, message = process_transcript_file(file_path, cleaner)
        
        if success:
            print(f"    ✓ {message}")
            successful += 1
            # Extrakcia počtu odstránených znakov
            if "odstránené" in message:
                try:
                    reduction = int(message.split("odstránené ")[1].split(" znakov")[0])
                    total_reduction += reduction
                except:
                    pass
        else:
            print(f"    ✗ {message}")
            failed += 1
        
        # Krátka pauza
        time.sleep(0.1)
    
    print(f"\n=== VÝSLEDOK ===")
    print(f"✅ Úspešne spracované: {successful}")
    print(f"❌ Neúspešné: {failed}")
    print(f"📊 Celkom odstránených znakov: {total_reduction:,}")
    print(f"📁 Vyčistené súbory majú príponu '_cleaned.txt'")
    print(f"💾 Uložené v: {text_dir}")

if __name__ == "__main__":
    main()
