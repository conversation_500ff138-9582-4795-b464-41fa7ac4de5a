#!/usr/bin/env python3
"""
Jednoduchý skript na odstránenie metadát
"""

import os
import re

def clean_file(file_path):
    """Vyčist<PERSON> jeden súbor od metadát"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        clean_lines = []
        
        # Preskočenie hlavičky
        start_found = False
        for line in lines:
            line_stripped = line.strip()
            
            # Preskočenie metadát
            if any(skip in line_stripped for skip in [
                'EPIZÓDA:', 'Opravené:', 'K<PERSON>ita:', 'Podcast:', 
                'INFORMÁCIE:', 'Dátum:', 'Nástroj:', 'Pôvodný súbor:',
                '=' * 5, '-' * 5, '*Epizóda*', '*Spracované*'
            ]):
                continue
            
            # Ak je riadok len zo š<PERSON>iálnych znakov
            if re.match(r'^[=\-*#\s]*$', line_stripped):
                continue
            
            # Ak sme našli prvý riadok príbehu
            if line_stripped and not start_found:
                start_found = True
            
            if start_found:
                clean_lines.append(line)
        
        # Spojenie a vyčistenie
        clean_text = '\n'.join(clean_lines).strip()
        
        # Odstránenie metadát na konci
        clean_text = re.sub(r'\n\s*=+.*$', '', clean_text, flags=re.DOTALL)
        clean_text = re.sub(r'\n\s*-+.*$', '', clean_text, flags=re.DOTALL)
        
        # Čistenie medzier
        clean_text = re.sub(r'\n{3,}', '\n\n', clean_text)
        clean_text = clean_text.strip()
        
        return clean_text
        
    except Exception as e:
        print(f"Chyba: {e}")
        return None

def main():
    """Hlavná funkcia"""
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    clean_dir = "/Users/<USER>/Desktop/Krvavý Audio/clean_stories"
    
    print("🧹 Čistím súbory od metadát...")
    print(f"Zdroj: {corrected_dir}")
    print(f"Cieľ: {clean_dir}")
    
    # Vytvorenie cieľového priečinka
    os.makedirs(clean_dir, exist_ok=True)
    
    if not os.path.exists(corrected_dir):
        print("❌ Zdrojový priečinok neexistuje!")
        return
    
    # Nájdenie súborov
    files = [f for f in os.listdir(corrected_dir) if f.endswith('_OpenAI_corrected.txt')]
    
    if not files:
        print("❌ Žiadne súbory na čistenie!")
        return
    
    print(f"📊 Našiel som {len(files)} súborov")
    
    successful = 0
    
    for i, file_name in enumerate(files, 1):
        episode_name = file_name.replace('_OpenAI_corrected.txt', '')
        file_path = os.path.join(corrected_dir, file_name)
        
        print(f"[{i}/{len(files)}] {episode_name}")
        
        # Čistenie
        clean_text = clean_file(file_path)
        
        if clean_text and len(clean_text) > 50:
            # Uloženie
            clean_file_path = os.path.join(clean_dir, f"{episode_name}.txt")
            
            with open(clean_file_path, 'w', encoding='utf-8') as f:
                f.write(clean_text)
            
            print(f"   ✅ Vyčistené ({len(clean_text)} znakov)")
            successful += 1
        else:
            print(f"   ❌ Problém s čistením")
    
    print(f"\n🎉 Hotovo! Vyčistených {successful} súborov")
    print(f"📁 Čisté súbory: {clean_dir}")

if __name__ == "__main__":
    main()
