#!/usr/bin/env python3
"""
Jednoduchý skript na odstránenie poznámky o podcaste
"""

import os
import re

def main():
    """Hlavná funkcia"""
    print("🧹 Odstraňujem poznámku o podcaste...")
    
    # Priečinky na spracovanie
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    directories = ["web_ready", "clean_stories"]
    
    total_processed = 0
    
    for dir_name in directories:
        dir_path = os.path.join(base_dir, dir_name)
        
        print(f"\n📂 {dir_name}: {dir_path}")
        
        if not os.path.exists(dir_path):
            print(f"   ❌ Neexistuje")
            continue
        
        # Nájdenie TXT súborov
        txt_files = [f for f in os.listdir(dir_path) if f.endswith('.txt')]
        print(f"   📊 Súborov: {len(txt_files)}")
        
        processed = 0
        
        for file_name in txt_files:
            file_path = os.path.join(dir_path, file_name)
            
            try:
                # Načítanie súboru
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Odstránenie poznámky o podcaste
                original_length = len(content)
                
                # Rôzne varianty na odstránenie
                content = re.sub(r'\*Epizóda podcastu Krvavý Dobšinský\*', '', content)
                content = re.sub(r'Epizóda podcastu Krvavý Dobšinský', '', content)
                content = re.sub(r'# .*\n', '', content)  # Markdown hlavičky
                content = re.sub(r'\*Spracované:.*?\*', '', content)
                content = re.sub(r'\*Kvalita:.*?\*', '', content)
                content = re.sub(r'---\s*\n', '', content)  # Oddeľovače
                
                # Vyčistenie medzier
                content = re.sub(r'\n{3,}', '\n\n', content)
                content = content.strip()
                
                # Uloženie ak sa zmenil
                if len(content) != original_length:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    processed += 1
                
            except Exception as e:
                print(f"   ❌ Chyba v {file_name}: {e}")
        
        print(f"   ✅ Spracovaných: {processed}")
        total_processed += processed
    
    print(f"\n🎉 Hotovo! Spracovaných {total_processed} súborov")
    
    # Test ukážky
    clean_dir = os.path.join(base_dir, "clean_stories")
    if os.path.exists(clean_dir):
        test_files = [f for f in os.listdir(clean_dir) if f.endswith('.txt')]
        if test_files:
            test_file = test_files[0]
            test_path = os.path.join(clean_dir, test_file)
            
            print(f"\n📄 Ukážka: {test_file}")
            
            with open(test_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"Dĺžka: {len(content)} znakov")
            print("Prvých 200 znakov:")
            print(content[:200])

if __name__ == "__main__":
    main()
