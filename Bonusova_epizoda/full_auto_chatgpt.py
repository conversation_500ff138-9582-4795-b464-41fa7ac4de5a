#!/usr/bin/env python3
"""
Plne automatizovaný skript pre opravu textu cez ChatGPT
"""

import os
import time
import subprocess
import pyautogui
import pyperclip
from pathlib import Path

class FullAutoChatGPT:
    def __init__(self):
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
        
        # Nastavenie pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
    
    def get_text_file(self):
        """Vyberie textový súbor na opravu"""
        prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        
        if not os.path.exists(prepisy_dir):
            return None
        
        # Nájdenie prvého _cleaned.txt súboru
        for file in sorted(os.listdir(prepisy_dir)):
            if file.endswith('_cleaned.txt'):
                return os.path.join(prepisy_dir, file)
        
        return None
    
    def extract_story_text(self, file_path):
        """Extrahuje samotný príbeh z textového súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní súboru: {e}")
            return None
    
    def create_chatgpt_prompt(self, text, file_name):
        """Vytvorí prompt pre ChatGPT"""
        
        # Skrátenie textu ak je príliš dlhý
        if len(text) > 5000:
            text = text[:5000] + "\n\n[Text skrátený kvôli limitom ChatGPT]"
        
        prompt = f"""Oprav tento slovenský text z podcastu "Krvavý Dobšinský". 

ÚLOHA:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu (úvodzovky „", pomlčky –, bodky, čiarky)
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Vráť len opravený text bez komentárov

NÁZOV: {file_name}

TEXT:
{text}"""

        return prompt
    
    def open_chatgpt_and_send(self, prompt):
        """Otvorí ChatGPT a pošle prompt"""
        try:
            print("🚀 Otváram ChatGPT...")
            
            # Otvorenie ChatGPT aplikácie
            subprocess.run(['open', '-a', 'ChatGPT'], check=True)
            time.sleep(4)
            
            print("📝 Posielam prompt...")
            
            # Skopírovanie prompt-u do schránky
            pyperclip.copy(prompt)
            
            # Kliknutie do textového poľa a vloženie
            pyautogui.hotkey('cmd', 'v')
            time.sleep(1)
            
            # Odoslanie
            pyautogui.press('enter')
            
            print("✅ Prompt odoslaný")
            print("⏳ Čakám na odpoveď ChatGPT...")
            
            # Čakanie na odpoveď
            time.sleep(25)
            
            return True
            
        except Exception as e:
            print(f"❌ Chyba pri posielaní: {e}")
            return False
    
    def get_chatgpt_response(self):
        """Získa odpoveď z ChatGPT"""
        try:
            print("📋 Získavam odpoveď...")
            
            # Pokus o výber celej odpovede
            # Klikneme na posledný text a vyberieme ho
            pyautogui.hotkey('cmd', 'a')
            time.sleep(1)
            
            # Kopírovanie
            pyautogui.hotkey('cmd', 'c')
            time.sleep(2)
            
            # Získanie textu zo schránky
            response = pyperclip.paste()
            
            print("✅ Odpoveď získaná")
            return response
            
        except Exception as e:
            print(f"❌ Chyba pri získavaní odpovede: {e}")
            return None
    
    def clean_chatgpt_response(self, response, original_prompt):
        """Vyčistí odpoveď ChatGPT od prompt-u"""
        try:
            # Odstránenie pôvodného prompt-u z odpovede
            if original_prompt in response:
                response = response.replace(original_prompt, "")
            
            # Hľadanie začiatku opraveného textu
            lines = response.split('\n')
            cleaned_lines = []
            found_start = False
            
            for line in lines:
                # Preskočenie prázdnych riadkov na začiatku
                if not found_start and line.strip():
                    found_start = True
                
                if found_start:
                    cleaned_lines.append(line)
            
            cleaned_text = '\n'.join(cleaned_lines).strip()
            
            # Odstránenie možných ChatGPT komentárov na konci
            if "Opravený text:" in cleaned_text:
                cleaned_text = cleaned_text.split("Opravený text:")[-1].strip()
            
            return cleaned_text
            
        except Exception as e:
            print(f"❌ Chyba pri čistení odpovede: {e}")
            return response
    
    def save_corrected_text(self, corrected_text, original_file_path):
        """Uloží opravený text"""
        try:
            # Vytvorenie názvu súboru
            original_name = os.path.splitext(os.path.basename(original_file_path))[0]
            if original_name.endswith('_cleaned'):
                original_name = original_name[:-8]
            
            output_file = os.path.join(self.corrected_dir, f"{original_name}_ChatGPT_corrected.txt")
            
            # Vytvorenie obsahu súboru
            content = [
                f"EPIZÓDA: {original_name}",
                "=" * 60,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: ChatGPT aplikácia",
                "Kvalita: Profesionálna slovenčina",
                "",
                corrected_text,
                "",
                "",
                "=" * 60,
                "INFORMÁCIE:",
                f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: ChatGPT aplikácia (automaticky)",
                f"- Pôvodný súbor: {os.path.basename(original_file_path)}",
                "- Opravené: gramatika, pravopis, diakritika, štruktúra",
                "- Jazyk: slovenčina (SK)"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ Opravený text uložený: {os.path.basename(output_file)}")
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_file_automatically(self, file_path):
        """Automaticky spracuje súbor cez ChatGPT"""
        print(f"\n🎯 === AUTOMATICKÉ SPRACOVANIE ===")
        print(f"📄 Súbor: {os.path.basename(file_path)}")
        
        # 1. Extrakcia textu
        story_text = self.extract_story_text(file_path)
        if not story_text:
            print("❌ Nepodarilo sa extrahovať text")
            return False
        
        print(f"📖 Text extrahovaný ({len(story_text)} znakov)")
        
        # 2. Vytvorenie prompt-u
        file_name = os.path.splitext(os.path.basename(file_path))[0].replace('_cleaned', '')
        prompt = self.create_chatgpt_prompt(story_text, file_name)
        
        # 3. Poslanie do ChatGPT
        if not self.open_chatgpt_and_send(prompt):
            return False
        
        # 4. Získanie odpovede
        response = self.get_chatgpt_response()
        if not response:
            return False
        
        # 5. Vyčistenie odpovede
        corrected_text = self.clean_chatgpt_response(response, prompt)
        
        # 6. Uloženie
        output_file = self.save_corrected_text(corrected_text, file_path)
        if not output_file:
            return False
        
        print(f"🎉 Súbor úspešne spracovaný!")
        
        # Zobrazenie ukážky
        print(f"\n📖 UKÁŽKA OPRAVENÉHO TEXTU:")
        print("-" * 50)
        preview = corrected_text[:300] + "..." if len(corrected_text) > 300 else corrected_text
        print(preview)
        print("-" * 50)
        
        return True

def main():
    """Hlavná funkcia"""
    print("🤖 === PLNE AUTOMATICKÁ OPRAVA CEZ CHATGPT ===")
    print()
    
    processor = FullAutoChatGPT()
    
    # Výber súboru
    file_path = processor.get_text_file()
    if not file_path:
        print("❌ Nenašiel sa žiadny súbor na spracovanie!")
        return
    
    print(f"📁 Priečinok corrected: {processor.corrected_dir}")
    print(f"🎯 Vybraný súbor: {os.path.basename(file_path)}")
    print()
    
    print("⚠️ UPOZORNENIE:")
    print("- Skript bude automaticky ovládať ChatGPT aplikáciu")
    print("- Nepracujte s počítačom počas spracovania")
    print("- Proces trvá približne 30-40 sekúnd")
    print()
    
    print("🚀 Začínam automatické spracovanie...")
    time.sleep(2)
    
    # Automatické spracovanie
    success = processor.process_file_automatically(file_path)
    
    if success:
        print(f"\n🎉 === ÚSPECH ===")
        print(f"✅ Text bol automaticky opravený cez ChatGPT")
        print(f"📁 Opravený súbor je v: {processor.corrected_dir}")
    else:
        print(f"\n❌ === CHYBA ===")
        print(f"Nepodarilo sa automaticky spracovať súbor")

if __name__ == "__main__":
    main()
