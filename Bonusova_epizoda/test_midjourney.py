#!/usr/bin/env python3
"""
Test Midjourney promptov
"""

import os

def test_midjourney():
    """Test Midjourney súborov"""
    
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    midjourney_dir = os.path.join(base_dir, "midjourney_prompts")
    
    print("🔍 Test Midjourney promptov")
    print(f"Hľadám: {midjourney_dir}")
    print(f"Existuje: {os.path.exists(midjourney_dir)}")
    
    if os.path.exists(midjourney_dir):
        files = os.listdir(midjourney_dir)
        print(f"Súborov: {len(files)}")
        
        txt_files = [f for f in files if f.endswith('.txt')]
        print(f"TXT súborov: {len(txt_files)}")
        
        if txt_files:
            print("\nPrvých 5 súborov:")
            for file in txt_files[:5]:
                print(f"  - {file}")
            
            # Test jedného súboru
            test_file = txt_files[0]
            test_path = os.path.join(midjourney_dir, test_file)
            
            print(f"\n📄 Test súboru: {test_file}")
            
            try:
                with open(test_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"Dĺžka: {len(content)} znakov")
                print("Prvých 500 znakov:")
                print("-" * 50)
                print(content[:500])
                print("-" * 50)
                
            except Exception as e:
                print(f"Chyba: {e}")
    else:
        print("❌ Priečinok neexistuje")
        
        # Skúsime nájsť všade
        print("\n🔍 Hľadám všade...")
        for root, dirs, files in os.walk("/Users/<USER>/Desktop"):
            if "midjourney" in root.lower():
                print(f"Našiel som: {root}")
                if files:
                    print(f"  Súbory: {len(files)}")

if __name__ == "__main__":
    test_midjourney()
