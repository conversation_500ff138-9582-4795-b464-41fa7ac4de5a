#!/usr/bin/env python3
"""
Dôkladné vyčistenie všetkých súborov od akýchkoľvek metadát
"""

import os
import re

def extract_pure_story(content):
    """Extrahuje len čistý príbeh"""
    lines = content.split('\n')
    story_lines = []
    
    # Preskočenie všetkých metadát a hlavičiek
    for line in lines:
        line_stripped = line.strip()
        
        # Preskočenie prázdnych riadkov
        if not line_stripped:
            if story_lines:  # Pridať prázdny riadok len ak už máme obsah
                story_lines.append('')
            continue
        
        # Preskočenie všetkých typov metadát
        skip_patterns = [
            r'^EPIZÓDA:',
            r'^Opravené:',
            r'^Opravené pomocou:',
            r'^Kvalita:',
            r'^Podcast:',
            r'^INFORMÁCIE:',
            r'^Dátum:',
            r'^Nástroj:',
            r'^Pôvodný súbor:',
            r'^Dĺžka:',
            r'^Jazyk:',
            r'^\*Epizóda podcastu',
            r'^\*Spracované:',
            r'^\*Kvalita:',
            r'^# ',
            r'^=+$',
            r'^-+$',
            r'^\*+$',
            r'^TEXT:$',
            r'INFORMÁCIE O OPRAVE:',
            r'slovenčina \(SK\)'
        ]
        
        # Kontrola, či riadok obsahuje metadata
        is_metadata = False
        for pattern in skip_patterns:
            if re.match(pattern, line_stripped, re.IGNORECASE):
                is_metadata = True
                break
        
        # Preskočenie riadkov s len špeciálnymi znakmi
        if re.match(r'^[=\-*#\s]*$', line_stripped):
            is_metadata = True
        
        # Ak nie je metadata, pridať do príbehu
        if not is_metadata:
            story_lines.append(line)
    
    # Spojenie a vyčistenie
    story_text = '\n'.join(story_lines)
    
    # Odstránenie viacnásobných prázdnych riadkov
    story_text = re.sub(r'\n{3,}', '\n\n', story_text)
    story_text = story_text.strip()
    
    return story_text

def clean_file(file_path):
    """Vyčistí jeden súbor"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extrakcia čistého príbehu
        clean_story = extract_pure_story(content)
        
        # Kontrola, či máme rozumný obsah
        if len(clean_story) < 50:
            print(f"   ⚠️  Príliš krátky obsah ({len(clean_story)} znakov)")
            return False
        
        # Uloženie vyčisteného obsahu
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(clean_story)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Chyba: {e}")
        return False

def main():
    """Hlavná funkcia"""
    print("🧹 === DÔKLADNÉ VYČISTENIE VŠETKÝCH SÚBOROV ===")
    print("Odstraňujem všetky metadata a technické informácie")
    print()
    
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    
    # Priečinky na vyčistenie
    directories = [
        ("clean_stories", "Čisté príbehy"),
        ("web_ready", "Web-ready súbory")
    ]
    
    total_processed = 0
    
    for dir_name, description in directories:
        dir_path = os.path.join(base_dir, dir_name)
        
        print(f"📂 {description}: {dir_path}")
        
        if not os.path.exists(dir_path):
            print(f"   ❌ Priečinok neexistuje")
            continue
        
        # Nájdenie TXT súborov
        txt_files = [f for f in os.listdir(dir_path) if f.endswith('.txt')]
        print(f"   📊 Nájdených {len(txt_files)} TXT súborov")
        
        if not txt_files:
            continue
        
        processed = 0
        
        for i, file_name in enumerate(txt_files, 1):
            file_path = os.path.join(dir_path, file_name)
            
            print(f"   [{i:2d}/{len(txt_files)}] {file_name[:50]}...")
            
            if clean_file(file_path):
                processed += 1
                print(f"      ✅ Vyčistené")
            else:
                print(f"      ❌ Problém")
        
        print(f"   🎉 Spracovaných: {processed}/{len(txt_files)}")
        total_processed += processed
        print()
    
    print(f"🏁 === FINÁLNY VÝSLEDOK ===")
    print(f"✅ Celkom vyčistených súborov: {total_processed}")
    print(f"🧹 Všetky metadata odstránené")
    print(f"📚 Súbory obsahujú len čistý text príbehov")
    
    # Ukážka vyčisteného súboru
    clean_dir = os.path.join(base_dir, "clean_stories")
    if os.path.exists(clean_dir):
        test_files = [f for f in os.listdir(clean_dir) if f.endswith('.txt')]
        if test_files:
            test_file = test_files[0]
            test_path = os.path.join(clean_dir, test_file)
            
            print(f"\n📄 Ukážka vyčisteného súboru: {test_file}")
            
            try:
                with open(test_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📊 Dĺžka: {len(content)} znakov")
                print("📖 Prvých 400 znakov:")
                print("-" * 60)
                print(content[:400])
                if len(content) > 400:
                    print("...")
                print("-" * 60)
                
            except Exception as e:
                print(f"❌ Chyba pri ukážke: {e}")

if __name__ == "__main__":
    main()
