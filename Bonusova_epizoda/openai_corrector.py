#!/usr/bin/env python3
"""
Pokročilý korektor textov pomocou OpenAI API
"""

import os
import time
import requests
import json

class OpenAICorrector:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
    
    def get_text_files(self):
        """Získa všetky textové súbory na opravu"""
        prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        
        if not os.path.exists(prepisy_dir):
            return []
        
        txt_files = []
        for file in sorted(os.listdir(prepisy_dir)):
            if file.endswith('_cleaned.txt'):
                txt_files.append(os.path.join(prepisy_dir, file))
        
        return txt_files
    
    def extract_story_text(self, file_path):
        """Extrahuje príbeh z súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní {file_path}: {e}")
            return None
    
    def create_correction_prompt(self, text, episode_name):
        """Vytvorí prompt pre OpenAI"""
        prompt = f"""Oprav tento slovenský text z podcastu "Krvavý Dobšinský". Ide o horor príbeh, zachovaj jeho atmosféru a štýl.

ÚLOHY:
1. Oprav všetky gramatické chyby a preklepy
2. Oprav pravopis a diakritiku (á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž)
3. Oprav interpunkciu - použij slovenské úvodzovky „" a pomlčky –
4. Rozdeľ text na logické odseky pre lepšiu čitateľnosť
5. Odstráň výplňové slová (ehm, no, tak, teda, vlastne, atď.)
6. Zachovaj pôvodný obsah, štýl a atmosféru príbehu
7. Neodstraňuj dôležité detaily ani dialógy
8. Vráť len opravený text bez komentárov

EPIZÓDA: {episode_name}

TEXT NA OPRAVU:
{text}

OPRAVENÝ TEXT:"""
        
        return prompt
    
    def correct_text_with_openai(self, text, episode_name):
        """Opraví text pomocou OpenAI API"""
        try:
            # Skrátenie textu ak je príliš dlhý
            if len(text) > 12000:
                text = text[:12000] + "\n\n[Text skrátený kvôli limitom API]"
            
            prompt = self.create_correction_prompt(text, episode_name)
            
            payload = {
                "model": "gpt-4",
                "messages": [
                    {
                        "role": "system",
                        "content": "Si expert na slovenský jazyk a korektúru textov. Opravuješ texty z horor podcastu s maximálnou presnosťou."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.3
            }
            
            print(f"📡 Posielam požiadavku do OpenAI...")
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                corrected_text = result['choices'][0]['message']['content'].strip()
                
                # Vyčistenie odpovede
                if "OPRAVENÝ TEXT:" in corrected_text:
                    corrected_text = corrected_text.split("OPRAVENÝ TEXT:")[-1].strip()
                
                return corrected_text
            else:
                print(f"❌ Chyba API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Chyba pri volaní OpenAI: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, original_file_path, episode_name):
        """Uloží opravený text"""
        try:
            output_file = os.path.join(self.corrected_dir, f"{episode_name}_OpenAI_corrected.txt")
            
            content = [
                f"EPIZÓDA: {episode_name}",
                "=" * 70,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: OpenAI GPT-4",
                "Kvalita: Profesionálna slovenčina",
                "",
                corrected_text,
                "",
                "",
                "=" * 70,
                "INFORMÁCIE O OPRAVE:",
                f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: OpenAI GPT-4 API",
                f"- Pôvodný súbor: {os.path.basename(original_file_path)}",
                f"- Dĺžka opraveného textu: {len(corrected_text)} znakov",
                "- Opravené: gramatika, pravopis, diakritika, interpunkcia, štruktúra",
                "- Jazyk: slovenčina (SK) - profesionálna úroveň",
                "- Zachovaný štýl: horor podcast"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ Uložené: {os.path.basename(output_file)}")
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_file(self, file_path):
        """Spracuje jeden súbor"""
        episode_name = os.path.basename(file_path).replace('_cleaned.txt', '')
        
        print(f"\n📄 Spracovávam: {episode_name}")
        
        # Kontrola či už existuje opravená verzia
        existing_file = os.path.join(self.corrected_dir, f"{episode_name}_OpenAI_corrected.txt")
        if os.path.exists(existing_file):
            print(f"⏭️  Preskakujem - už existuje: {os.path.basename(existing_file)}")
            return True
        
        # Extrakcia textu
        story_text = self.extract_story_text(file_path)
        if not story_text:
            print(f"❌ Nepodarilo sa extrahovať text")
            return False
        
        print(f"📖 Text extrahovaný ({len(story_text)} znakov)")
        
        # Oprava cez OpenAI
        corrected_text = self.correct_text_with_openai(story_text, episode_name)
        if not corrected_text:
            print(f"❌ Nepodarilo sa opraviť text")
            return False
        
        print(f"🔧 Text opravený ({len(corrected_text)} znakov)")
        
        # Uloženie
        output_file = self.save_corrected_text(corrected_text, file_path, episode_name)
        if not output_file:
            return False
        
        return True
    
    def process_all_files(self, max_files=None):
        """Spracuje všetky súbory"""
        txt_files = self.get_text_files()
        
        if not txt_files:
            print("❌ Nenašli sa žiadne súbory na spracovanie!")
            return
        
        if max_files:
            txt_files = txt_files[:max_files]
        
        print(f"🎯 Spracovávam {len(txt_files)} súborov pomocou OpenAI GPT-4")
        print(f"📁 Výstupný priečinok: {self.corrected_dir}")
        print()
        
        successful = 0
        failed = 0
        skipped = 0
        
        for i, file_path in enumerate(txt_files, 1):
            print(f"[{i}/{len(txt_files)}] " + "="*50)
            
            result = self.process_file(file_path)
            
            if result is True:
                successful += 1
            elif result is None:  # Preskočené
                skipped += 1
            else:
                failed += 1
            
            # Pauza medzi požiadavkami
            if i < len(txt_files):
                print("⏳ Pauza 2 sekundy...")
                time.sleep(2)
        
        print(f"\n🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne spracované: {successful}")
        print(f"⏭️  Preskočené: {skipped}")
        print(f"❌ Neúspešné: {failed}")
        print(f"📁 Opravené súbory: {self.corrected_dir}")

def main():
    """Hlavná funkcia"""
    print("🤖 === AUTOMATICKÁ OPRAVA CEZ OPENAI API ===")
    print()
    
    # API kľúč
    api_key = "********************************************************************************************************************************************************************"
    
    corrector = OpenAICorrector(api_key)
    
    print("🎯 Možnosti:")
    print("1. Opraviť prvých 3 súborov (test)")
    print("2. Opraviť prvých 10 súborov")
    print("3. Opraviť všetky súbory")
    
    choice = input("\nVyberte možnosť (1/2/3): ").strip()
    
    if choice == "1":
        corrector.process_all_files(max_files=3)
    elif choice == "2":
        corrector.process_all_files(max_files=10)
    elif choice == "3":
        corrector.process_all_files()
    else:
        print("❌ Neplatná voľba!")

if __name__ == "__main__":
    main()
