#!/usr/bin/env python3
"""
Odstráni "*Epizóda podcastu Krvavý Dobšinský*" zo všetkých súborov
"""

import os
import re

def clean_podcast_note_from_file(file_path):
    """Odstráni poznámku o podcaste zo súboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Odstránenie rôznych variácií poznámky o podcaste
        patterns_to_remove = [
            r'\*Epizóda podcastu Krvavý Dobšinský\*',
            r'Epizóda podcastu Krvavý Dobšinský',
            r'\*Epizóda podcastu.*?\*',
            r'# .*\n',  # Markdown hlavičky
            r'\*Spracované:.*?\*',
            r'\*Kvalita:.*?\*',
            r'---\s*\n',  # Markdown oddeľovače
        ]
        
        # Aplikovanie všetkých vzorov
        for pattern in patterns_to_remove:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.MULTILINE)
        
        # Vyčistenie viacnásobných nových riadkov
        content = re.sub(r'\n{3,}', '\n\n', content)
        content = content.strip()
        
        return content
        
    except Exception as e:
        print(f"❌ Chyba pri čítaní {file_path}: {e}")
        return None

def process_directory(directory_path, description):
    """Spracuje všetky súbory v priečinku"""
    if not os.path.exists(directory_path):
        print(f"⚠️  Priečinok {description} neexistuje: {directory_path}")
        return 0
    
    print(f"\n📂 Spracovávam {description}: {directory_path}")
    
    txt_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]
    
    if not txt_files:
        print(f"   📄 Žiadne TXT súbory")
        return 0
    
    print(f"   📊 Nájdených {len(txt_files)} TXT súborov")
    
    processed = 0
    
    for file_name in txt_files:
        file_path = os.path.join(directory_path, file_name)
        
        # Čistenie súboru
        clean_content = clean_podcast_note_from_file(file_path)
        
        if clean_content is not None:
            # Prepísanie súboru
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(clean_content)
            
            processed += 1
    
    print(f"   ✅ Spracovaných: {processed} súborov")
    return processed

def main():
    """Hlavná funkcia"""
    print("🧹 === ODSTRÁNENIE POZNÁMKY O PODCASTE ===")
    print("Odstraňujem '*Epizóda podcastu Krvavý Dobšinský*' zo všetkých súborov")
    print()
    
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    
    # Priečinky na spracovanie
    directories = [
        (os.path.join(base_dir, "web_ready"), "web_ready"),
        (os.path.join(base_dir, "clean_stories"), "clean_stories"),
        (os.path.join(base_dir, "corrected"), "corrected")
    ]
    
    total_processed = 0
    
    for dir_path, dir_name in directories:
        processed = process_directory(dir_path, dir_name)
        total_processed += processed
    
    print(f"\n🏁 === FINÁLNY VÝSLEDOK ===")
    print(f"✅ Celkom spracovaných súborov: {total_processed}")
    print(f"🧹 Poznámka '*Epizóda podcastu Krvavý Dobšinský*' odstránená")
    print(f"📚 Súbory sú teraz úplne čisté")
    
    # Test jedného súboru
    clean_dir = os.path.join(base_dir, "clean_stories")
    if os.path.exists(clean_dir):
        test_files = [f for f in os.listdir(clean_dir) if f.endswith('.txt')]
        if test_files:
            test_file = test_files[0]
            test_path = os.path.join(clean_dir, test_file)
            
            print(f"\n📄 Ukážka vyčisteného súboru: {test_file}")
            
            try:
                with open(test_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📊 Dĺžka: {len(content)} znakov")
                print("📖 Prvých 300 znakov:")
                print("-" * 50)
                print(content[:300])
                if len(content) > 300:
                    print("...")
                print("-" * 50)
                
            except Exception as e:
                print(f"❌ Chyba pri teste: {e}")

if __name__ == "__main__":
    main()
