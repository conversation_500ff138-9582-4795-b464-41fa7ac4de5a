#!/usr/bin/env python3
"""
Generátor Midjourney promptov pre epizódy Krvavý Dobšinský
Analyzuje obsah každej epizódy a vytvorí vizuálny prompt
"""

import os
import re
import time

class MidjourneyPromptGenerator:
    def __init__(self):
        self.clean_dir = "/Users/<USER>/Desktop/Krvavý Audio/clean_stories"
        self.output_dir = "/Users/<USER>/Desktop/Krvavý Audio/midjourney_prompts"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Kľúčové slová pre rôzne vizuálne elementy
        self.visual_keywords = {
            'locations': {
                'hrad': 'ancient castle', 'kaštie<PERSON>': 'manor house', 'dom': 'old house',
                'les': 'dark forest', 'cintorín': 'cemetery', 'kostol': 'church',
                'dedina': 'village', 'mesto': 'town', 'tunel': 'tunnel',
                'jaskyňa': 'cave', 'dolina': 'valley', 'hora': 'mountain',
                'rieka': 'river', 'jazero': 'lake', 'most': 'bridge',
                'mlyn': 'old mill', 'pivnica': 'cellar', 'podkrovie': 'attic'
            },
            'characters': {
                'žena': 'woman', 'muž': 'man', 'dieťa': 'child', 'starec': 'old man',
                'stará': 'old woman', 'duch': 'ghost', 'upír': 'vampire',
                'vlkolak': 'werewolf', 'čarodejnica': 'witch', 'démon': 'demon',
                'baba jaga': 'baba yaga', 'mŕtvy': 'dead person', 'zombie': 'zombie'
            },
            'atmosphere': {
                'tma': 'darkness', 'hmla': 'fog', 'búrka': 'storm', 'dážď': 'rain',
                'sneh': 'snow', 'mráz': 'frost', 'vietor': 'wind', 'mesiac': 'moon',
                'hviezdy': 'stars', 'oheň': 'fire', 'sviečka': 'candle',
                'strach': 'fear', 'hrôza': 'horror', 'tajomno': 'mystery'
            },
            'objects': {
                'krv': 'blood', 'kosti': 'bones', 'lebka': 'skull', 'rakva': 'coffin',
                'kríž': 'cross', 'zrkadlo': 'mirror', 'fotka': 'photograph',
                'kniha': 'book', 'list': 'letter', 'kľúč': 'key', 'dvere': 'door',
                'okno': 'window', 'schody': 'stairs', 'kreslo': 'armchair'
            }
        }
    
    def extract_key_elements(self, text, episode_name):
        """Extrahuje kľúčové vizuálne elementy z textu"""
        text_lower = text.lower()
        
        # Nájdenie lokácií
        locations = []
        for slovak, english in self.visual_keywords['locations'].items():
            if slovak in text_lower:
                locations.append(english)
        
        # Nájdenie postav
        characters = []
        for slovak, english in self.visual_keywords['characters'].items():
            if slovak in text_lower:
                characters.append(english)
        
        # Nájdenie atmosféry
        atmosphere = []
        for slovak, english in self.visual_keywords['atmosphere'].items():
            if slovak in text_lower:
                atmosphere.append(english)
        
        # Nájdenie objektov
        objects = []
        for slovak, english in self.visual_keywords['objects'].items():
            if slovak in text_lower:
                objects.append(english)
        
        # Špecifické analýzy podľa názvu epizódy
        episode_lower = episode_name.lower()
        
        # Pridanie špecifických elementov podľa názvu
        if 'babylon' in episode_lower:
            locations.append('ancient babylon')
            atmosphere.append('mystical')
        elif 'upír' in episode_lower or 'vampire' in episode_lower:
            characters.append('vampire')
            atmosphere.append('gothic')
        elif 'vlkolak' in episode_lower:
            characters.append('werewolf')
            locations.append('forest')
        elif 'baba jaga' in episode_lower:
            characters.append('baba yaga')
            locations.append('witch hut')
        elif 'zmok' in episode_lower:
            characters.append('dragon')
            atmosphere.append('medieval')
        elif 'hrad' in episode_lower:
            locations.append('medieval castle')
        elif 'tunel' in episode_lower:
            locations.append('dark tunnel')
            atmosphere.append('claustrophobic')
        
        return {
            'locations': list(set(locations))[:3],  # Max 3
            'characters': list(set(characters))[:2],  # Max 2
            'atmosphere': list(set(atmosphere))[:3],  # Max 3
            'objects': list(set(objects))[:2]  # Max 2
        }
    
    def create_midjourney_prompt(self, episode_name, elements):
        """Vytvorí Midjourney prompt z extrahovaných elementov"""
        
        # Základný štýl pre horor
        base_style = "dark horror atmosphere, cinematic lighting, dramatic shadows"
        
        # Zostavenie hlavnej scény
        scene_parts = []
        
        # Lokácia
        if elements['locations']:
            scene_parts.append(f"in {', '.join(elements['locations'])}")
        
        # Postavy
        if elements['characters']:
            scene_parts.append(f"featuring {', '.join(elements['characters'])}")
        
        # Objekty
        if elements['objects']:
            scene_parts.append(f"with {', '.join(elements['objects'])}")
        
        # Atmosféra
        atmosphere_desc = ', '.join(elements['atmosphere']) if elements['atmosphere'] else 'eerie, mysterious'
        
        # Zostavenie finálneho promptu
        main_scene = ' '.join(scene_parts) if scene_parts else "mysterious horror scene"
        
        prompt = f"{main_scene}, {atmosphere_desc}, {base_style}, highly detailed, photorealistic, 8k resolution, --ar 16:9 --style raw --v 6"
        
        return prompt
    
    def create_alternative_prompts(self, episode_name, text):
        """Vytvorí alternatívne prompty pre rôzne štýly"""
        
        # Základné elementy
        elements = self.extract_key_elements(text, episode_name)
        
        prompts = {}
        
        # 1. Klasický horor
        prompts['classic_horror'] = self.create_midjourney_prompt(episode_name, elements)
        
        # 2. Gotický štýl
        gothic_elements = elements.copy()
        gothic_style = "gothic architecture, medieval atmosphere, stone textures, dramatic lighting"
        main_scene = f"gothic horror scene"
        if elements['locations']:
            main_scene += f" in {elements['locations'][0]}"
        prompts['gothic'] = f"{main_scene}, {gothic_style}, highly detailed, --ar 16:9 --style raw --v 6"
        
        # 3. Slovenský folklór
        folklore_style = "Slovak folklore, traditional clothing, rural setting, folk art style"
        folklore_scene = f"Slovak folk horror tale"
        if elements['characters']:
            folklore_scene += f" with {elements['characters'][0]}"
        prompts['folklore'] = f"{folklore_scene}, {folklore_style}, illustrated style, --ar 16:9 --v 6"
        
        # 4. Minimalistický
        minimal_style = "minimalist composition, stark contrast, single focal point, dramatic shadows"
        minimal_scene = "minimalist horror scene"
        if elements['objects']:
            minimal_scene += f" featuring {elements['objects'][0]}"
        prompts['minimal'] = f"{minimal_scene}, {minimal_style}, black and white, --ar 1:1 --style raw --v 6"
        
        return prompts
    
    def process_episode(self, file_path, episode_name):
        """Spracuje jednu epizódu"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            if len(text) < 100:
                print(f"   ⚠️  Text príliš krátky")
                return None
            
            # Vytvorenie promptov
            prompts = self.create_alternative_prompts(episode_name, text)
            
            # Vytvorenie súboru s promptmi
            prompt_file = os.path.join(self.output_dir, f"{episode_name}_midjourney_prompts.txt")
            
            content = [
                f"MIDJOURNEY PROMPTY PRE: {episode_name}",
                "=" * 80,
                f"Generované: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"Zdroj: Krvavý Dobšinský podcast",
                "",
                "🎨 KLASICKÝ HOROR:",
                prompts['classic_horror'],
                "",
                "🏰 GOTICKÝ ŠTÝL:",
                prompts['gothic'],
                "",
                "🎭 SLOVENSKÝ FOLKLÓR:",
                prompts['folklore'],
                "",
                "⚫ MINIMALISTICKÝ:",
                prompts['minimal'],
                "",
                "=" * 80,
                "POKYNY PRE POUŽITIE:",
                "1. Skopírujte vybraný prompt do Midjourney",
                "2. Môžete upraviť --ar (aspect ratio) podľa potreby",
                "3. Pridajte --seed pre konzistentné výsledky",
                "4. Experimentujte s --stylize hodnoty (0-1000)",
                "",
                "ODPORÚČANÉ ÚPRAVY:",
                "- Pre tmavšiu atmosféru: pridajte 'noir lighting'",
                "- Pre historický vzhľad: pridajte 'vintage, sepia tones'",
                "- Pre moderný horor: pridajte 'modern horror, urban setting'",
                "",
                f"Epizóda: {episode_name}",
                f"Podcast: Krvavý Dobšinský"
            ]
            
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            return prompts['classic_horror']
            
        except Exception as e:
            print(f"   ❌ Chyba: {e}")
            return None
    
    def create_master_prompt_file(self, all_prompts):
        """Vytvorí hlavný súbor so všetkými promptmi"""
        master_file = os.path.join(self.output_dir, "ALL_MIDJOURNEY_PROMPTS.txt")
        
        content = [
            "VŠETKY MIDJOURNEY PROMPTY - KRVAVÝ DOBŠINSKÝ",
            "=" * 80,
            f"Generované: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Celkom epizód: {len(all_prompts)}",
            f"Podcast: Krvavý Dobšinský",
            "",
            "🎨 KLASICKÉ HOROR PROMPTY PRE VŠETKY EPIZÓDY:",
            "=" * 80,
            ""
        ]
        
        for i, (episode_name, prompt) in enumerate(all_prompts.items(), 1):
            content.extend([
                f"{i:2d}. {episode_name}:",
                f"    {prompt}",
                ""
            ])
        
        content.extend([
            "=" * 80,
            "POKYNY PRE HROMADNÉ POUŽITIE:",
            "",
            "1. BATCH GENEROVANIE:",
            "   - Použite každý prompt samostatne",
            "   - Pridajte konzistentné parametre (--seed, --style)",
            "   - Zachovajte jednotný aspect ratio",
            "",
            "2. KONZISTENTNÝ ŠTÝL:",
            "   - Všetky prompty majú 'dark horror atmosphere'",
            "   - Odporúčané: --ar 16:9 pre landscape",
            "   - Odporúčané: --style raw pre realistickejší vzhľad",
            "",
            "3. VARIÁCIE:",
            "   - Pridajte 'Slovak folklore' pre lokálny štýl",
            "   - Pridajte 'gothic architecture' pre historický vzhľad",
            "   - Pridajte 'cinematic' pre filmový štýl",
            "",
            "4. KVALITA:",
            "   - Všetky prompty optimalizované pre v6",
            "   - Obsahujú 'highly detailed, photorealistic'",
            "   - Nastavené na 8k resolution",
            "",
            f"Celkom promptov: {len(all_prompts)}",
            f"Dátum: {time.strftime('%Y-%m-%d')}",
            "Zdroj: Krvavý Dobšinský podcast"
        ])
        
        with open(master_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        return master_file
    
    def process_all_episodes(self):
        """Spracuje všetky epizódy"""
        print("🎨 === GENEROVANIE MIDJOURNEY PROMPTOV ===")
        print(f"📁 Zdrojový priečinok: {self.clean_dir}")
        print(f"📁 Výstupný priečinok: {self.output_dir}")
        print()
        
        if not os.path.exists(self.clean_dir):
            print(f"❌ Zdrojový priečinok neexistuje!")
            return
        
        # Nájdenie všetkých epizód
        txt_files = [f for f in os.listdir(self.clean_dir) if f.endswith('.txt')]
        
        if not txt_files:
            print("❌ Nenašli sa žiadne súbory!")
            return
        
        print(f"📊 Spracovávam {len(txt_files)} epizód")
        print()
        
        successful = 0
        all_prompts = {}
        
        for i, file_name in enumerate(sorted(txt_files), 1):
            episode_name = file_name.replace('.txt', '')
            file_path = os.path.join(self.clean_dir, file_name)
            
            print(f"[{i:2d}/{len(txt_files)}] {episode_name}")
            
            prompt = self.process_episode(file_path, episode_name)
            
            if prompt:
                all_prompts[episode_name] = prompt
                print(f"   ✅ Prompt vytvorený")
                successful += 1
            else:
                print(f"   ❌ Nepodarilo sa vytvoriť prompt")
            
            print()
        
        # Vytvorenie hlavného súboru
        if all_prompts:
            master_file = self.create_master_prompt_file(all_prompts)
            print(f"📋 Hlavný súbor vytvorený: {os.path.basename(master_file)}")
        
        # Finálny súhrn
        print(f"🏁 === FINÁLNY VÝSLEDOK ===")
        print(f"✅ Úspešne spracované: {successful}")
        print(f"❌ Neúspešné: {len(txt_files) - successful}")
        print(f"📁 Prompty uložené v: {self.output_dir}")
        print(f"📋 Hlavný súbor: ALL_MIDJOURNEY_PROMPTS.txt")
        
        # Ukážka promptov
        if all_prompts:
            print(f"\n🎨 Ukážka prvých 3 promptov:")
            for i, (episode, prompt) in enumerate(list(all_prompts.items())[:3], 1):
                print(f"\n{i}. {episode}:")
                print(f"   {prompt[:100]}...")

def main():
    """Hlavná funkcia"""
    generator = MidjourneyPromptGenerator()
    generator.process_all_episodes()

if __name__ == "__main__":
    main()
