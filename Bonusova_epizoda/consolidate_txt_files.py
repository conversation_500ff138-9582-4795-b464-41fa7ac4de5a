#!/usr/bin/env python3
"""
Presunie všetky TXT súbory do priečinka Prepisy
"""

import os
import shutil
import time

def find_all_txt_files(root_dir):
    """Nájde všetky TXT súbory v celom priečinku"""
    txt_files = []
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.lower().endswith('.txt'):
                file_path = os.path.join(root, file)
                txt_files.append(file_path)
    
    return txt_files

def categorize_txt_files(txt_files, prepisy_dir):
    """Kategorizuje TXT súbory podľa umiestnenia"""
    
    files_in_prepisy = []
    files_outside_prepisy = []
    
    for file_path in txt_files:
        if prepisy_dir in file_path:
            files_in_prepisy.append(file_path)
        else:
            files_outside_prepisy.append(file_path)
    
    return files_in_prepisy, files_outside_prepisy

def move_txt_files_to_prepisy():
    """Presunie všetky TXT súbory do priečinka Prepisy"""
    
    root_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    
    print("📁 === KONSOLIDÁCIA TXT SÚBOROV ===")
    print(f"🔍 Hľadám TXT súbory v: {root_dir}")
    print(f"🎯 Cieľový priečinok: {prepisy_dir}")
    print()
    
    # Vytvorenie priečinka Prepisy ak neexistuje
    os.makedirs(prepisy_dir, exist_ok=True)
    
    # Nájdenie všetkých TXT súborov
    print("🔍 Hľadám všetky TXT súbory...")
    txt_files = find_all_txt_files(root_dir)
    
    if not txt_files:
        print("❌ Nenašli sa žiadne TXT súbory!")
        return
    
    print(f"📊 Nájdených {len(txt_files)} TXT súborov")
    
    # Kategorizácia súborov
    files_in_prepisy, files_outside_prepisy = categorize_txt_files(txt_files, prepisy_dir)
    
    print(f"   ✅ Už v Prepisy: {len(files_in_prepisy)}")
    print(f"   📦 Na presun: {len(files_outside_prepisy)}")
    print()
    
    if not files_outside_prepisy:
        print("🎉 Všetky TXT súbory sú už v priečinku Prepisy!")
        
        # Zobrazenie obsahu priečinka Prepisy
        print(f"\n📋 Obsah priečinka Prepisy:")
        try:
            prepisy_files = [f for f in os.listdir(prepisy_dir) if f.endswith('.txt')]
            prepisy_files.sort()
            
            for i, file in enumerate(prepisy_files[:15], 1):
                file_path = os.path.join(prepisy_dir, file)
                size_kb = os.path.getsize(file_path) / 1024
                print(f"   {i:2d}. {file} ({size_kb:.1f} KB)")
            
            if len(prepisy_files) > 15:
                print(f"   ... a ďalších {len(prepisy_files)-15} súborov")
            
            print(f"\n📊 Celkom TXT súborov v Prepisy: {len(prepisy_files)}")
            
        except Exception as e:
            print(f"❌ Chyba pri čítaní priečinka: {e}")
        
        return
    
    # Zobrazenie súborov na presun
    print("📦 Súbory na presun:")
    for i, file_path in enumerate(files_outside_prepisy, 1):
        relative_path = file_path.replace(root_dir, "")
        file_size = os.path.getsize(file_path) / 1024
        print(f"   {i:2d}. {relative_path} ({file_size:.1f} KB)")
    
    print()
    
    # Presun súborov
    successful = 0
    failed = 0
    
    for file_path in files_outside_prepisy:
        file_name = os.path.basename(file_path)
        target_path = os.path.join(prepisy_dir, file_name)
        
        try:
            # Kontrola či súbor už existuje v cieli
            if os.path.exists(target_path):
                print(f"⚠️  Súbor už existuje: {file_name}")
                
                # Vytvorenie záložnej kópie
                base_name, ext = os.path.splitext(file_name)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                backup_name = f"{base_name}_backup_{timestamp}{ext}"
                backup_path = os.path.join(prepisy_dir, backup_name)
                
                shutil.move(target_path, backup_path)
                print(f"   📋 Existujúci súbor premenovaný na: {backup_name}")
            
            # Presun súboru
            shutil.move(file_path, target_path)
            print(f"✅ Presunuté: {file_name}")
            successful += 1
            
        except Exception as e:
            print(f"❌ Chyba pri presune {file_name}: {e}")
            failed += 1
    
    print(f"\n🏁 === VÝSLEDOK KONSOLIDÁCIE ===")
    print(f"✅ Úspešne presunuté: {successful}")
    print(f"❌ Neúspešné: {failed}")
    print(f"📁 Všetky TXT súbory sú teraz v: {prepisy_dir}")
    
    # Finálny prehľad
    try:
        final_files = [f for f in os.listdir(prepisy_dir) if f.endswith('.txt')]
        print(f"📊 Celkový počet TXT súborov v Prepisy: {len(final_files)}")
        
        # Zobrazenie typov súborov
        types = {}
        for file in final_files:
            if '_cleaned.txt' in file:
                types['cleaned'] = types.get('cleaned', 0) + 1
            elif '_corrected.txt' in file:
                types['corrected'] = types.get('corrected', 0) + 1
            elif '_info.txt' in file:
                types['info'] = types.get('info', 0) + 1
            elif '_prepis.txt' in file:
                types['prepis'] = types.get('prepis', 0) + 1
            else:
                types['other'] = types.get('other', 0) + 1
        
        print(f"\n📋 Typy súborov:")
        for file_type, count in types.items():
            print(f"   {file_type}: {count} súborov")
        
    except Exception as e:
        print(f"❌ Chyba pri finálnom prehľade: {e}")

def clean_empty_directories():
    """Vyčistí prázdne priečinky po presune súborov"""
    
    root_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
    corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
    
    print(f"\n🧹 Čistím prázdne priečinky...")
    
    removed_dirs = 0
    
    for root, dirs, files in os.walk(root_dir, topdown=False):
        # Preskočiť hlavné priečinky
        if root in [root_dir, prepisy_dir, corrected_dir]:
            continue
        
        try:
            # Kontrola či je priečinok prázdny
            if not os.listdir(root):
                os.rmdir(root)
                print(f"🗑️  Odstránený prázdny priečinok: {root.replace(root_dir, '')}")
                removed_dirs += 1
        except Exception as e:
            print(f"⚠️  Nepodarilo sa odstrániť {root}: {e}")
    
    if removed_dirs == 0:
        print("✅ Žiadne prázdne priečinky na odstránenie")
    else:
        print(f"✅ Odstránených {removed_dirs} prázdnych priečinkov")

def main():
    """Hlavná funkcia"""
    move_txt_files_to_prepisy()
    clean_empty_directories()
    
    print(f"\n🎉 Konsolidácia TXT súborov dokončená!")
    print(f"📁 Všetky TXT súbory sú teraz v priečinku Prepisy")

if __name__ == "__main__":
    main()
