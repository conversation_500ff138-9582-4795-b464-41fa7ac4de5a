#!/usr/bin/env python3
"""
Skript na stiahnutie všetkých audio súborov z RSS feedu podcastu Krvavý Dobšinský
"""

import os
import re
import requests
import xml.etree.ElementTree as ET
from urllib.parse import urlparse
import time
from pathlib import Path

def sanitize_filename(filename):
    """Odstráni nepovolené znaky z názvu súboru"""
    # Odstráni HTML tagy
    filename = re.sub(r'<[^>]+>', '', filename)
    # Odstráni nepovolené znaky pre názov súboru
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # Nahradí viacnásobné medzery jednou
    filename = re.sub(r'\s+', ' ', filename)
    # Odstráni medzery na začiatku a konci
    filename = filename.strip()
    # Skr<PERSON>ti názov ak je pr<PERSON><PERSON><PERSON> dlh<PERSON>
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def get_desktop_path():
    """Získa cestu k ploche"""
    home = Path.home()
    desktop = home / "Desktop"
    if not desktop.exists():
        # Pre macOS môže byť Desktop v inom jazyku
        desktop = home / "Plocha"
        if not desktop.exists():
            desktop = home / "Bureau"  # francúzština
            if not desktop.exists():
                desktop = home / "Escritorio"  # španielčina
                if not desktop.exists():
                    desktop = home  # fallback na home directory
    return desktop

def download_file(url, filepath, episode_title):
    """Stiahne súbor z URL"""
    try:
        print(f"Sťahujem: {episode_title}")
        print(f"URL: {url}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rProgress: {percent:.1f}%", end='', flush=True)
        
        print(f"\n✓ Stiahnuté: {filepath}")
        return True
        
    except Exception as e:
        print(f"\n✗ Chyba pri sťahovaní {episode_title}: {e}")
        return False

def parse_rss_and_download(rss_url, download_dir):
    """Parsuje RSS feed a stiahne všetky audio súbory"""
    try:
        print(f"Načítavam RSS feed: {rss_url}")
        response = requests.get(rss_url)
        response.raise_for_status()
        
        # Parsovanie XML
        root = ET.fromstring(response.content)
        
        # Nájdenie všetkých epizód
        episodes = []
        for item in root.findall('.//item'):
            title_elem = item.find('title')
            enclosure_elem = item.find('enclosure')
            
            if title_elem is not None and enclosure_elem is not None:
                title = title_elem.text
                audio_url = enclosure_elem.get('url')
                
                if title and audio_url:
                    episodes.append({
                        'title': title,
                        'url': audio_url
                    })
        
        print(f"Nájdených {len(episodes)} epizód")
        
        if not episodes:
            print("Nenašli sa žiadne audio súbory v RSS feede")
            return
        
        # Vytvorenie adresára ak neexistuje
        os.makedirs(download_dir, exist_ok=True)
        
        # Stiahnutie každej epizódy
        successful_downloads = 0
        for i, episode in enumerate(episodes, 1):
            print(f"\n[{i}/{len(episodes)}]")
            
            # Vytvorenie názvu súboru
            safe_title = sanitize_filename(episode['title'])
            
            # Získanie prípony súboru z URL
            parsed_url = urlparse(episode['url'])
            file_extension = os.path.splitext(parsed_url.path)[1]
            if not file_extension:
                file_extension = '.mp3'  # default prípona
            
            filename = f"{safe_title}{file_extension}"
            filepath = os.path.join(download_dir, filename)
            
            # Kontrola či súbor už existuje
            if os.path.exists(filepath):
                print(f"Súbor už existuje: {filename}")
                successful_downloads += 1
                continue
            
            # Stiahnutie súboru
            if download_file(episode['url'], filepath, episode['title']):
                successful_downloads += 1
                time.sleep(1)  # Krátka pauza medzi stiahnutiami
        
        print(f"\n\nHotovo! Úspešne stiahnutých: {successful_downloads}/{len(episodes)} epizód")
        print(f"Súbory sú uložené v: {download_dir}")
        
    except Exception as e:
        print(f"Chyba pri spracovaní RSS feedu: {e}")

def main():
    """Hlavná funkcia"""
    rss_url = "https://anchor.fm/s/8db2e1ec/podcast/rss"
    
    # Vytvorenie adresára na ploche
    desktop_path = get_desktop_path()
    download_dir = desktop_path / "Krvavý Audio"
    
    print("=== Sťahovač podcastu Krvavý Dobšinský ===")
    print(f"RSS URL: {rss_url}")
    print(f"Cieľový adresár: {download_dir}")
    print()
    
    parse_rss_and_download(rss_url, str(download_dir))

if __name__ == "__main__":
    main()
