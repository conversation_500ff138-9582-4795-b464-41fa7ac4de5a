#!/usr/bin/env python3
"""
Skript na automatickú opravu textov cez ChatGPT aplikáciu
"""

import os
import time
import subprocess
import pyautogui
import pyperclip
from pathlib import Path

class ChatGPTCorrector:
    def __init__(self):
        self.corrected_dir = "/Users/<USER>/Desktop/Krvavý Audio/corrected"
        os.makedirs(self.corrected_dir, exist_ok=True)
        
        # Nastavenie pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 1
    
    def get_random_text_file(self):
        """Vyberie náhodný textový súbor na opravu"""
        prepisy_dir = "/Users/<USER>/Desktop/Krvavý Audio/Prepisy"
        
        if not os.path.exists(prepisy_dir):
            return None
        
        # Nájdenie všetkých _cleaned.txt súborov
        txt_files = []
        for file in os.listdir(prepisy_dir):
            if file.endswith('_cleaned.txt'):
                txt_files.append(os.path.join(prepisy_dir, file))
        
        if not txt_files:
            return None
        
        # Vrátime prvý súbor pre test
        return txt_files[0]
    
    def extract_story_text(self, file_path):
        """Extrahuje samotný príbeh z textového súboru"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            story_start = 0
            story_end = len(lines)
            
            # Nájdenie začiatku príbehu
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in 
                       ['prepis:', 'prepis', 'text:', '---', 'príbeh']):
                    story_start = i + 1
                    break
            
            # Nájdenie konca príbehu
            for i in range(len(lines)-1, -1, -1):
                if any(keyword in line.lower() for keyword in 
                       ['informácie', 'spracované:', 'koniec', '===']):
                    story_end = i
                    break
            
            story_lines = lines[story_start:story_end]
            story_text = '\n'.join(story_lines).strip()
            
            return story_text
            
        except Exception as e:
            print(f"Chyba pri čítaní súboru: {e}")
            return None
    
    def open_chatgpt_app(self):
        """Otvorí ChatGPT aplikáciu"""
        try:
            print("🚀 Otváram ChatGPT aplikáciu...")
            
            # Pokus o otvorenie cez Spotlight
            pyautogui.hotkey('cmd', 'space')
            time.sleep(1)
            pyautogui.write('ChatGPT')
            time.sleep(1)
            pyautogui.press('enter')
            time.sleep(3)
            
            print("✅ ChatGPT aplikácia otvorená")
            return True
            
        except Exception as e:
            print(f"❌ Chyba pri otváraní ChatGPT: {e}")
            return False
    
    def send_correction_request(self, text, file_name):
        """Pošle text na opravu do ChatGPT"""
        try:
            # Vytvorenie prompt-u
            prompt = f"""Oprav tento slovenský text z podcastu "Krvavý Dobšinský". 

ÚLOHA:
1. Oprav všetky gramatické chyby
2. Oprav pravopis a diakritiku
3. Oprav interpunkciu
4. Rozdeľ text na logické odseky
5. Odstráň výplňové slová (ehm, no, tak, atď.)
6. Zachovaj pôvodný obsah a štýl
7. Vráť len opravený text bez komentárov

NÁZOV EPIZÓDY: {file_name}

TEXT NA OPRAVU:
{text}

OPRAVENÝ TEXT:"""

            print("📝 Posielam text na opravu...")
            
            # Skopírovanie prompt-u do schránky
            pyperclip.copy(prompt)
            
            # Kliknutie do textového poľa ChatGPT
            # Predpokladáme, že ChatGPT je otvorený a aktívny
            time.sleep(2)
            
            # Vloženie textu
            pyautogui.hotkey('cmd', 'v')
            time.sleep(1)
            
            # Odoslanie
            pyautogui.press('enter')
            
            print("✅ Text odoslaný na opravu")
            print("⏳ Čakám na odpoveď ChatGPT...")
            
            # Čakanie na odpoveď (30 sekúnd)
            time.sleep(30)
            
            return True
            
        except Exception as e:
            print(f"❌ Chyba pri posielaní textu: {e}")
            return False
    
    def copy_corrected_text(self):
        """Skopíruje opravený text z ChatGPT"""
        try:
            print("📋 Kopírujem opravený text...")
            
            # Výber celého textu odpovede
            pyautogui.hotkey('cmd', 'a')
            time.sleep(1)
            
            # Kopírovanie
            pyautogui.hotkey('cmd', 'c')
            time.sleep(2)
            
            # Získanie textu zo schránky
            corrected_text = pyperclip.paste()
            
            print("✅ Text skopírovaný")
            return corrected_text
            
        except Exception as e:
            print(f"❌ Chyba pri kopírovaní: {e}")
            return None
    
    def save_corrected_text(self, corrected_text, original_file_path):
        """Uloží opravený text do priečinka corrected"""
        try:
            # Vytvorenie názvu súboru
            original_name = os.path.splitext(os.path.basename(original_file_path))[0]
            if original_name.endswith('_cleaned'):
                original_name = original_name[:-8]  # Odstránenie '_cleaned'
            
            output_file = os.path.join(self.corrected_dir, f"{original_name}_ChatGPT_corrected.txt")
            
            # Vytvorenie obsahu súboru
            content = [
                f"EPIZÓDA: {original_name}",
                "=" * 60,
                f"Opravené: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "Opravené pomocou: ChatGPT",
                "Kvalita: Profesionálna slovenčina",
                "",
                corrected_text,
                "",
                "",
                "=" * 60,
                "INFORMÁCIE:",
                f"- Dátum opravy: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- Nástroj: ChatGPT aplikácia",
                f"- Pôvodný súbor: {os.path.basename(original_file_path)}",
                "- Opravené: gramatika, pravopis, diakritika, štruktúra",
                "- Jazyk: slovenčina (SK)"
            ]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ Opravený text uložený: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ Chyba pri ukladaní: {e}")
            return None
    
    def process_file(self, file_path):
        """Spracuje jeden súbor cez ChatGPT"""
        print(f"\n🎯 === SPRACOVANIE SÚBORU ===")
        print(f"📄 Súbor: {os.path.basename(file_path)}")
        
        # 1. Extrakcia textu
        story_text = self.extract_story_text(file_path)
        if not story_text:
            print("❌ Nepodarilo sa extrahovať text")
            return False
        
        print(f"📖 Extrahovaný text ({len(story_text)} znakov)")
        
        # Skrátenie textu ak je príliš dlhý (ChatGPT má limity)
        if len(story_text) > 8000:
            story_text = story_text[:8000] + "\n\n[Text skrátený kvôli limitom ChatGPT]"
            print("⚠️ Text skrátený kvôli limitom ChatGPT")
        
        # 2. Otvorenie ChatGPT
        if not self.open_chatgpt_app():
            return False
        
        # 3. Poslanie na opravu
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        if not self.send_correction_request(story_text, file_name):
            return False
        
        # 4. Kopírovanie opraveného textu
        corrected_text = self.copy_corrected_text()
        if not corrected_text:
            return False
        
        # 5. Uloženie
        output_file = self.save_corrected_text(corrected_text, file_path)
        if not output_file:
            return False
        
        print(f"🎉 Súbor úspešne spracovaný!")
        return True

def main():
    """Hlavná funkcia"""
    print("🤖 === AUTOMATICKÁ OPRAVA CEZ CHATGPT ===")
    print()
    
    corrector = ChatGPTCorrector()
    
    # Výber súboru
    file_path = corrector.get_random_text_file()
    if not file_path:
        print("❌ Nenašiel sa žiadny súbor na spracovanie!")
        return
    
    print(f"📁 Priečinok corrected: {corrector.corrected_dir}")
    print()
    
    # Potvrdenie
    print(f"🎯 Vybraný súbor: {os.path.basename(file_path)}")
    print()
    print("⚠️ UPOZORNENIE:")
    print("1. Uistite sa, že máte nainštalovanú ChatGPT aplikáciu")
    print("2. Skript bude automaticky ovládať myš a klávesnicu")
    print("3. Nepracujte s počítačom počas spracovania")
    print("4. Proces trvá približne 1-2 minúty")
    print()
    
    input("Stlačte Enter pre pokračovanie alebo Ctrl+C pre zrušenie...")
    
    # Spracovanie
    success = corrector.process_file(file_path)
    
    if success:
        print(f"\n🎉 === ÚSPECH ===")
        print(f"✅ Súbor bol úspešne opravený cez ChatGPT")
        print(f"📁 Opravený súbor je v: {corrector.corrected_dir}")
    else:
        print(f"\n❌ === CHYBA ===")
        print(f"Nepodarilo sa spracovať súbor")

if __name__ == "__main__":
    main()
