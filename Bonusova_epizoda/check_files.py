#!/usr/bin/env python3
"""
Kontrola súborov v priečinkoch
"""

import os

def check_directories():
    """Skontroluje všetky priečinky"""
    
    base_dir = "/Users/<USER>/Desktop/Krvavý Audio"
    
    print("📁 === KONTROLA PRIEČINKOV ===")
    print(f"Základný priečinok: {base_dir}")
    print()
    
    # Kontrola existencie základného priečinka
    if not os.path.exists(base_dir):
        print(f"❌ Základný priečinok neexistuje: {base_dir}")
        return
    
    # Kontrola všetkých podpriečinkov
    subdirs = ['Prepisy', 'corrected', 'web_ready']
    
    for subdir in subdirs:
        full_path = os.path.join(base_dir, subdir)
        print(f"📂 {subdir}:")
        print(f"   Cesta: {full_path}")
        
        if os.path.exists(full_path):
            try:
                files = os.listdir(full_path)
                txt_files = [f for f in files if f.endswith('.txt')]
                
                print(f"   ✅ Existuje")
                print(f"   📊 Celkom súborov: {len(files)}")
                print(f"   📄 TXT súborov: {len(txt_files)}")
                
                if txt_files:
                    print(f"   📋 Prvých 5 TXT súborov:")
                    for i, file in enumerate(txt_files[:5], 1):
                        print(f"      {i}. {file}")
                    
                    if len(txt_files) > 5:
                        print(f"      ... a ďalších {len(txt_files)-5}")
                
            except Exception as e:
                print(f"   ❌ Chyba pri čítaní: {e}")
        else:
            print(f"   ❌ Neexistuje")
        
        print()
    
    # Kontrola OpenAI súborov
    corrected_path = os.path.join(base_dir, 'corrected')
    if os.path.exists(corrected_path):
        try:
            all_files = os.listdir(corrected_path)
            openai_files = [f for f in all_files if 'OpenAI' in f and f.endswith('.txt')]
            
            print(f"🤖 OpenAI opravené súbory:")
            print(f"   📊 Počet: {len(openai_files)}")
            
            if openai_files:
                print(f"   📋 Prvých 10:")
                for i, file in enumerate(openai_files[:10], 1):
                    print(f"      {i:2d}. {file}")
                
                if len(openai_files) > 10:
                    print(f"      ... a ďalších {len(openai_files)-10}")
            
        except Exception as e:
            print(f"   ❌ Chyba: {e}")

if __name__ == "__main__":
    check_directories()
